"""Add enterprise organization and multi-tenancy tables

Revision ID: 20250620_add_enterprise_organization_tables
Revises: 20250619_add_isf_nist_csf_framework_tables
Create Date: 2025-06-20 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '20250620_add_enterprise_organization_tables'
down_revision = '20250619_add_isf_nist_csf_framework_tables'
branch_labels = None
depends_on = None


def upgrade():
    """Add enterprise organization and multi-tenancy tables."""
    
    # Create subscription tier enum
    subscription_tier_enum = postgresql.ENUM(
        'starter', 'professional', 'enterprise', 'custom',
        name='subscription_tier_enum'
    )
    subscription_tier_enum.create(op.get_bind())
    
    # Create resource type enum
    resource_type_enum = postgresql.ENUM(
        'compute', 'storage', 'network', 'database', 'cache',
        name='resource_type_enum'
    )
    resource_type_enum.create(op.get_bind())
    
    # Create invitation status enum
    invitation_status_enum = postgresql.ENUM(
        'pending', 'accepted', 'declined', 'expired',
        name='invitation_status_enum'
    )
    invitation_status_enum.create(op.get_bind())
    
    # Rename existing organizations table to organizations_legacy
    op.rename_table('organizations', 'organizations_legacy')
    
    # Create new enhanced organizations table
    op.create_table(
        'organizations',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True),
        sa.Column('name', sa.String(255), nullable=False, index=True),
        sa.Column('display_name', sa.String(255), nullable=False),
        sa.Column('description', sa.Text, nullable=True),
        sa.Column('subscription_tier', subscription_tier_enum, nullable=False, default='starter'),
        sa.Column('settings', sa.JSON, nullable=False, default={}),
        sa.Column('contact_info', sa.JSON, nullable=False, default={}),
        sa.Column('compliance_requirements', sa.JSON, nullable=False, default=[]),
        sa.Column('is_active', sa.Boolean, default=True, nullable=False),
        sa.Column('is_trial', sa.Boolean, default=False, nullable=False),
        sa.Column('trial_expires_at', sa.DateTime, nullable=True),
        sa.Column('legacy_id', sa.Integer, nullable=True, index=True),
        sa.Column('created_at', sa.DateTime, nullable=False, server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime, nullable=False, server_default=sa.func.now()),
        sa.Column('deleted_at', sa.DateTime, nullable=True),
    )
    
    # Create tenant resources table
    op.create_table(
        'tenant_resources',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True),
        sa.Column('tenant_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('resource_type', resource_type_enum, nullable=False),
        sa.Column('resource_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('allocated_at', sa.DateTime, nullable=False, server_default=sa.func.now()),
        sa.Column('usage_metrics', sa.JSON, nullable=False, default={}),
        sa.Column('limits', sa.JSON, nullable=False, default={}),
        sa.Column('is_active', sa.Boolean, default=True, nullable=False),
        sa.Column('created_at', sa.DateTime, nullable=False, server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime, nullable=False, server_default=sa.func.now()),
        sa.ForeignKeyConstraint(['tenant_id'], ['organizations.id']),
        sa.UniqueConstraint('tenant_id', 'resource_type', 'resource_id', name='uq_tenant_resource'),
    )
    
    # Create organization invitations table
    op.create_table(
        'organization_invitations',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True),
        sa.Column('organization_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('email', sa.String(255), nullable=False, index=True),
        sa.Column('role', sa.String(50), nullable=False, default='member'),
        sa.Column('invited_by', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('token', sa.String(255), nullable=False, unique=True, index=True),
        sa.Column('status', invitation_status_enum, nullable=False, default='pending'),
        sa.Column('expires_at', sa.DateTime, nullable=False),
        sa.Column('accepted_at', sa.DateTime, nullable=True),
        sa.Column('created_at', sa.DateTime, nullable=False, server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime, nullable=False, server_default=sa.func.now()),
        sa.ForeignKeyConstraint(['organization_id'], ['organizations.id']),
    )
    
    # Create organization usage table
    op.create_table(
        'organization_usage',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True),
        sa.Column('organization_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('period_start', sa.DateTime, nullable=False),
        sa.Column('period_end', sa.DateTime, nullable=False),
        sa.Column('active_users', sa.Integer, nullable=False, default=0),
        sa.Column('api_requests', sa.Integer, nullable=False, default=0),
        sa.Column('storage_used_gb', sa.Float, nullable=False, default=0.0),
        sa.Column('compute_hours', sa.Float, nullable=False, default=0.0),
        sa.Column('detailed_metrics', sa.JSON, nullable=False, default={}),
        sa.Column('billable_amount', sa.Float, nullable=True),
        sa.Column('currency', sa.String(3), nullable=False, default='USD'),
        sa.Column('created_at', sa.DateTime, nullable=False, server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime, nullable=False, server_default=sa.func.now()),
        sa.ForeignKeyConstraint(['organization_id'], ['organizations.id']),
        sa.UniqueConstraint('organization_id', 'period_start', 'period_end', name='uq_org_usage_period'),
    )
    
    # Create indexes for performance
    op.create_index('idx_organizations_name', 'organizations', ['name'])
    op.create_index('idx_organizations_subscription_tier', 'organizations', ['subscription_tier'])
    op.create_index('idx_organizations_is_active', 'organizations', ['is_active'])
    op.create_index('idx_organizations_created_at', 'organizations', ['created_at'])
    
    op.create_index('idx_tenant_resources_tenant_id', 'tenant_resources', ['tenant_id'])
    op.create_index('idx_tenant_resources_resource_type', 'tenant_resources', ['resource_type'])
    op.create_index('idx_tenant_resources_is_active', 'tenant_resources', ['is_active'])
    
    op.create_index('idx_organization_invitations_email', 'organization_invitations', ['email'])
    op.create_index('idx_organization_invitations_status', 'organization_invitations', ['status'])
    op.create_index('idx_organization_invitations_expires_at', 'organization_invitations', ['expires_at'])
    
    op.create_index('idx_organization_usage_period', 'organization_usage', ['period_start', 'period_end'])
    op.create_index('idx_organization_usage_organization_id', 'organization_usage', ['organization_id'])


def downgrade():
    """Remove enterprise organization and multi-tenancy tables."""
    
    # Drop indexes
    op.drop_index('idx_organization_usage_organization_id')
    op.drop_index('idx_organization_usage_period')
    op.drop_index('idx_organization_invitations_expires_at')
    op.drop_index('idx_organization_invitations_status')
    op.drop_index('idx_organization_invitations_email')
    op.drop_index('idx_tenant_resources_is_active')
    op.drop_index('idx_tenant_resources_resource_type')
    op.drop_index('idx_tenant_resources_tenant_id')
    op.drop_index('idx_organizations_created_at')
    op.drop_index('idx_organizations_is_active')
    op.drop_index('idx_organizations_subscription_tier')
    op.drop_index('idx_organizations_name')
    
    # Drop tables
    op.drop_table('organization_usage')
    op.drop_table('organization_invitations')
    op.drop_table('tenant_resources')
    op.drop_table('organizations')
    
    # Restore original organizations table
    op.rename_table('organizations_legacy', 'organizations')
    
    # Drop enums
    op.execute('DROP TYPE IF EXISTS invitation_status_enum')
    op.execute('DROP TYPE IF EXISTS resource_type_enum')
    op.execute('DROP TYPE IF EXISTS subscription_tier_enum')
