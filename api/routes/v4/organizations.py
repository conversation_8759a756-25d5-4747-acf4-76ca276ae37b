"""
Organization management API endpoints for multi-tenancy.

This module provides REST API endpoints for managing organizations,
including CRUD operations, user management, and subscription handling.
"""

from datetime import datetime, timed<PERSON>ta
from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_

from api.database import get_db
from api.auth.dependencies import get_current_user, require_admin
from api.models.organization import Organization, OrganizationInvitation, TenantResource
from api.models.user import User
from api.schemas.organization import (
    OrganizationCreate,
    OrganizationUpdate,
    OrganizationResponse,
    OrganizationListResponse,
    OrganizationInvitationCreate,
    OrganizationInvitationResponse,
    TenantResourceResponse,
    TenantResourceListResponse
)
from api.services.organization import OrganizationService
from api.utils.pagination import paginate
from api.middleware.rate_limiting import standard_rate_limit

router = APIRouter()


@router.post("", response_model=OrganizationResponse, status_code=status.HTTP_201_CREATED)
async def create_organization(
    organization_data: OrganizationCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    _: None = Depends(standard_rate_limit)
):
    """
    Create a new organization.
    
    Creates a new organization with the specified settings, subscription tier,
    and compliance requirements. The current user becomes the organization owner.
    """
    service = OrganizationService(db)
    
    try:
        organization = await service.create_organization(
            organization_data=organization_data,
            owner_id=current_user.id
        )
        return OrganizationResponse.from_orm(organization)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create organization"
        )


@router.get("", response_model=OrganizationListResponse)
async def list_organizations(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    search: Optional[str] = Query(None, description="Search by name or display name"),
    subscription_tier: Optional[str] = Query(None, description="Filter by subscription tier"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    _: None = Depends(standard_rate_limit)
):
    """
    List organizations with pagination and filtering.
    
    Returns a paginated list of organizations that the current user
    has access to, with optional filtering by various criteria.
    """
    query = db.query(Organization).filter(Organization.deleted_at.is_(None))
    
    # Apply search filter
    if search:
        search_filter = or_(
            Organization.name.ilike(f"%{search}%"),
            Organization.display_name.ilike(f"%{search}%")
        )
        query = query.filter(search_filter)
    
    # Apply subscription tier filter
    if subscription_tier:
        query = query.filter(Organization.subscription_tier == subscription_tier)
    
    # Apply active status filter
    if is_active is not None:
        query = query.filter(Organization.is_active == is_active)
    
    # For non-admin users, only show organizations they belong to
    if not current_user.is_admin:
        # This would need to be implemented based on user-organization relationships
        pass
    
    # Paginate results
    result = paginate(query, page=page, size=size)
    
    return OrganizationListResponse(
        items=[OrganizationResponse.from_orm(org) for org in result.items],
        total=result.total,
        page=page,
        size=size,
        pages=result.pages
    )


@router.get("/{organization_id}", response_model=OrganizationResponse)
async def get_organization(
    organization_id: UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    _: None = Depends(standard_rate_limit)
):
    """
    Get organization details by ID.
    
    Returns detailed information about a specific organization,
    including settings, subscription, and compliance requirements.
    """
    service = OrganizationService(db)
    
    organization = await service.get_organization(
        organization_id=organization_id,
        user_id=current_user.id
    )
    
    if not organization:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Organization not found"
        )
    
    return OrganizationResponse.from_orm(organization)


@router.put("/{organization_id}", response_model=OrganizationResponse)
async def update_organization(
    organization_id: UUID,
    organization_data: OrganizationUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    _: None = Depends(standard_rate_limit)
):
    """
    Update organization details.
    
    Updates the specified organization with new settings, subscription tier,
    or other configuration changes. Requires organization admin permissions.
    """
    service = OrganizationService(db)
    
    try:
        organization = await service.update_organization(
            organization_id=organization_id,
            organization_data=organization_data,
            user_id=current_user.id
        )
        
        if not organization:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Organization not found"
            )
        
        return OrganizationResponse.from_orm(organization)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except PermissionError:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to update organization"
        )


@router.delete("/{organization_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_organization(
    organization_id: UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    _: None = Depends(standard_rate_limit)
):
    """
    Delete (soft delete) an organization.
    
    Marks the organization as deleted and deactivates all associated
    resources. Requires organization owner permissions.
    """
    service = OrganizationService(db)
    
    try:
        success = await service.delete_organization(
            organization_id=organization_id,
            user_id=current_user.id
        )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Organization not found"
            )
    except PermissionError:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to delete organization"
        )


@router.post("/{organization_id}/users", response_model=OrganizationInvitationResponse)
async def invite_user_to_organization(
    organization_id: UUID,
    invitation_data: OrganizationInvitationCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    _: None = Depends(standard_rate_limit)
):
    """
    Invite a user to join the organization.
    
    Sends an invitation email to the specified user to join the organization
    with the specified role. Requires organization admin permissions.
    """
    service = OrganizationService(db)
    
    try:
        invitation = await service.invite_user(
            organization_id=organization_id,
            invitation_data=invitation_data,
            invited_by=current_user.id
        )
        
        return OrganizationInvitationResponse.from_orm(invitation)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except PermissionError:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to invite users"
        )


@router.delete("/{organization_id}/users/{user_id}", status_code=status.HTTP_204_NO_CONTENT)
async def remove_user_from_organization(
    organization_id: UUID,
    user_id: UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    _: None = Depends(standard_rate_limit)
):
    """
    Remove a user from the organization.
    
    Removes the specified user from the organization and revokes
    all associated permissions. Requires organization admin permissions.
    """
    service = OrganizationService(db)
    
    try:
        success = await service.remove_user(
            organization_id=organization_id,
            user_id=user_id,
            removed_by=current_user.id
        )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found in organization"
            )
    except PermissionError:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to remove users"
        )
