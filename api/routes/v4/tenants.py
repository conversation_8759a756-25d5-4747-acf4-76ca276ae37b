"""
Tenant isolation and resource management API endpoints.

This module provides REST API endpoints for managing tenant resources,
usage metrics, backup/restore operations, and data migration.
"""

from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session
from sqlalchemy import and_, func

from api.database import get_db
from api.auth.dependencies import get_current_user, require_admin
from api.models.organization import Organization, TenantResource, OrganizationUsage
from api.models.user import User
from api.schemas.organization import (
    TenantResourceCreate,
    TenantResourceResponse,
    TenantResourceListResponse,
    OrganizationUsageResponse,
    ResourceType
)
from api.services.tenant import TenantService
from api.utils.pagination import paginate
from api.middleware.rate_limiting import standard_rate_limit

router = APIRouter()


@router.get("/{tenant_id}/resources", response_model=TenantResourceListResponse)
async def get_tenant_resources(
    tenant_id: UUID,
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    resource_type: Optional[ResourceType] = Query(None, description="Filter by resource type"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    _: None = Depends(standard_rate_limit)
):
    """
    Get tenant resource allocations.
    
    Returns a paginated list of all resources allocated to the specified tenant,
    including usage metrics and limits.
    """
    # Verify tenant access
    tenant = db.query(Organization).filter(
        and_(
            Organization.id == tenant_id,
            Organization.deleted_at.is_(None)
        )
    ).first()
    
    if not tenant:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Tenant not found"
        )
    
    # TODO: Implement tenant access validation for current user
    
    # Build query
    query = db.query(TenantResource).filter(TenantResource.tenant_id == tenant_id)
    
    # Apply filters
    if resource_type:
        query = query.filter(TenantResource.resource_type == resource_type)
    
    if is_active is not None:
        query = query.filter(TenantResource.is_active == is_active)
    
    # Paginate results
    result = paginate(query, page=page, size=size)
    
    return TenantResourceListResponse(
        items=[TenantResourceResponse.from_orm(resource) for resource in result.items],
        total=result.total,
        page=page,
        size=size,
        pages=result.pages
    )


@router.post("/{tenant_id}/migrate", status_code=status.HTTP_202_ACCEPTED)
async def migrate_tenant_data(
    tenant_id: UUID,
    target_region: str = Query(..., description="Target region for migration"),
    include_historical_data: bool = Query(True, description="Include historical data in migration"),
    db: Session = Depends(get_db),
    current_user: User = Depends(require_admin),
    _: None = Depends(standard_rate_limit)
):
    """
    Migrate tenant data to a different region or infrastructure.
    
    Initiates a background migration process to move tenant data
    to the specified target region. Requires admin permissions.
    """
    service = TenantService(db)
    
    try:
        migration_job = await service.initiate_migration(
            tenant_id=tenant_id,
            target_region=target_region,
            include_historical_data=include_historical_data,
            initiated_by=current_user.id
        )
        
        return {
            "message": "Migration initiated successfully",
            "migration_job_id": migration_job.id,
            "estimated_completion": migration_job.estimated_completion,
            "status": "in_progress"
        }
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to initiate migration"
        )


@router.get("/{tenant_id}/usage", response_model=List[OrganizationUsageResponse])
async def get_tenant_usage_metrics(
    tenant_id: UUID,
    start_date: Optional[datetime] = Query(None, description="Start date for usage metrics"),
    end_date: Optional[datetime] = Query(None, description="End date for usage metrics"),
    granularity: str = Query("daily", regex="^(hourly|daily|weekly|monthly)$"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    _: None = Depends(standard_rate_limit)
):
    """
    Get tenant usage metrics for billing and monitoring.
    
    Returns usage metrics for the specified tenant within the given
    time range, aggregated by the specified granularity.
    """
    # Verify tenant access
    tenant = db.query(Organization).filter(
        and_(
            Organization.id == tenant_id,
            Organization.deleted_at.is_(None)
        )
    ).first()
    
    if not tenant:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Tenant not found"
        )
    
    # TODO: Implement tenant access validation for current user
    
    # Set default date range if not provided
    if not end_date:
        end_date = datetime.utcnow()
    if not start_date:
        start_date = end_date - timedelta(days=30)
    
    # Query usage metrics
    query = db.query(OrganizationUsage).filter(
        and_(
            OrganizationUsage.organization_id == tenant_id,
            OrganizationUsage.period_start >= start_date,
            OrganizationUsage.period_end <= end_date
        )
    ).order_by(OrganizationUsage.period_start)
    
    usage_records = query.all()
    
    return [OrganizationUsageResponse.from_orm(record) for record in usage_records]


@router.post("/{tenant_id}/backup", status_code=status.HTTP_202_ACCEPTED)
async def create_tenant_backup(
    tenant_id: UUID,
    backup_type: str = Query("full", regex="^(full|incremental|differential)$"),
    include_user_data: bool = Query(True, description="Include user data in backup"),
    retention_days: int = Query(30, ge=1, le=365, description="Backup retention period"),
    db: Session = Depends(get_db),
    current_user: User = Depends(require_admin),
    _: None = Depends(standard_rate_limit)
):
    """
    Create a backup of tenant data.
    
    Initiates a background backup process for the specified tenant.
    Supports full, incremental, and differential backup types.
    """
    service = TenantService(db)
    
    try:
        backup_job = await service.create_backup(
            tenant_id=tenant_id,
            backup_type=backup_type,
            include_user_data=include_user_data,
            retention_days=retention_days,
            initiated_by=current_user.id
        )
        
        return {
            "message": "Backup initiated successfully",
            "backup_job_id": backup_job.id,
            "backup_type": backup_type,
            "estimated_completion": backup_job.estimated_completion,
            "status": "in_progress"
        }
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to initiate backup"
        )


@router.post("/{tenant_id}/restore", status_code=status.HTTP_202_ACCEPTED)
async def restore_tenant_from_backup(
    tenant_id: UUID,
    backup_id: UUID = Query(..., description="Backup ID to restore from"),
    restore_point: Optional[datetime] = Query(None, description="Point-in-time to restore to"),
    overwrite_existing: bool = Query(False, description="Overwrite existing data"),
    db: Session = Depends(get_db),
    current_user: User = Depends(require_admin),
    _: None = Depends(standard_rate_limit)
):
    """
    Restore tenant data from a backup.
    
    Initiates a background restore process from the specified backup.
    Supports point-in-time recovery and selective data restoration.
    """
    service = TenantService(db)
    
    try:
        restore_job = await service.restore_from_backup(
            tenant_id=tenant_id,
            backup_id=backup_id,
            restore_point=restore_point,
            overwrite_existing=overwrite_existing,
            initiated_by=current_user.id
        )
        
        return {
            "message": "Restore initiated successfully",
            "restore_job_id": restore_job.id,
            "backup_id": backup_id,
            "estimated_completion": restore_job.estimated_completion,
            "status": "in_progress"
        }
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to initiate restore"
        )


@router.get("/{tenant_id}/health")
async def get_tenant_health_status(
    tenant_id: UUID,
    include_resources: bool = Query(True, description="Include resource health"),
    include_metrics: bool = Query(True, description="Include performance metrics"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    _: None = Depends(standard_rate_limit)
):
    """
    Get tenant health status and diagnostics.
    
    Returns comprehensive health information for the tenant,
    including resource status, performance metrics, and alerts.
    """
    service = TenantService(db)
    
    try:
        health_status = await service.get_health_status(
            tenant_id=tenant_id,
            include_resources=include_resources,
            include_metrics=include_metrics
        )
        
        return health_status
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve health status"
        )
