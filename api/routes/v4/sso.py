"""
Single Sign-On (SSO) and Identity Provider integration API endpoints.

This module provides REST API endpoints for configuring and managing
SSO providers including SAML2, OIDC, OAuth2, LDAP, and Active Directory.
"""

from typing import List, Optional, Dict, Any
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_

from api.database import get_db
from api.auth.dependencies import get_current_user, require_admin
from api.models.user import User
from api.models.sso import SSOProvider, SSOAuthSession
from api.schemas.sso import (
    SSOProviderCreate,
    SSOProviderUpdate,
    SSOProviderResponse,
    SSOProviderListResponse,
    SSOAuthSessionResponse,
    SSOAuthSessionListResponse,
    SSOTestRequest,
    SSOTestResult,
    SSOMetadataResponse,
    SSOProviderType,
    SSOProviderStatus
)
from api.services.sso import SSOService
from api.utils.pagination import paginate
from api.middleware.rate_limiting import standard_rate_limit

router = APIRouter()


@router.post("/providers", response_model=SSOProviderResponse, status_code=status.HTTP_201_CREATED)
async def configure_sso_provider(
    provider_data: SSOProviderCreate,
    organization_id: UUID = Query(..., description="Organization ID"),
    db: Session = Depends(get_db),
    current_user: User = Depends(require_admin),
    _: None = Depends(standard_rate_limit)
):
    """
    Configure a new SSO provider.

    Creates a new SSO provider configuration for SAML2, OIDC, OAuth2,
    LDAP, or Active Directory integration.
    """
    service = SSOService(db)

    try:
        provider = await service.create_sso_provider(
            organization_id=organization_id,
            provider_data=provider_data,
            created_by=current_user.id
        )
        return SSOProviderResponse.from_orm(provider)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create SSO provider"
        )


@router.get("/providers", response_model=SSOProviderListResponse)
async def list_sso_providers(
    organization_id: UUID = Query(..., description="Organization ID"),
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    provider_type: Optional[SSOProviderType] = Query(None, description="Filter by provider type"),
    status: Optional[SSOProviderStatus] = Query(None, description="Filter by status"),
    search: Optional[str] = Query(None, description="Search by name or display name"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    _: None = Depends(standard_rate_limit)
):
    """
    List configured SSO providers.

    Returns a paginated list of all configured SSO providers with their
    configuration status and metadata.
    """
    query = db.query(SSOProvider).filter(
        and_(
            SSOProvider.organization_id == organization_id,
            SSOProvider.deleted_at.is_(None)
        )
    )

    # Apply filters
    if provider_type:
        query = query.filter(SSOProvider.provider_type == provider_type)

    if status:
        query = query.filter(SSOProvider.status == status)

    if search:
        search_filter = or_(
            SSOProvider.name.ilike(f"%{search}%"),
            SSOProvider.display_name.ilike(f"%{search}%")
        )
        query = query.filter(search_filter)

    # Paginate results
    result = paginate(query, page=page, size=size)

    return SSOProviderListResponse(
        items=[SSOProviderResponse.from_orm(provider) for provider in result.items],
        total=result.total,
        page=page,
        size=size,
        pages=result.pages
    )


@router.get("/providers/{provider_id}", response_model=SSOProviderResponse)
async def get_sso_provider(
    provider_id: UUID,
    organization_id: UUID = Query(..., description="Organization ID"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    _: None = Depends(standard_rate_limit)
):
    """
    Get SSO provider details by ID.

    Returns detailed information about a specific SSO provider,
    including configuration and test results.
    """
    service = SSOService(db)

    provider = await service.get_sso_provider(provider_id, organization_id)

    if not provider:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="SSO provider not found"
        )

    return SSOProviderResponse.from_orm(provider)


@router.put("/providers/{provider_id}", response_model=SSOProviderResponse)
async def update_sso_provider(
    provider_id: UUID,
    provider_data: SSOProviderUpdate,
    organization_id: UUID = Query(..., description="Organization ID"),
    db: Session = Depends(get_db),
    current_user: User = Depends(require_admin),
    _: None = Depends(standard_rate_limit)
):
    """
    Update SSO provider configuration.

    Updates the configuration for an existing SSO provider,
    including certificates, endpoints, and attribute mappings.
    """
    service = SSOService(db)

    try:
        provider = await service.update_sso_provider(
            provider_id=provider_id,
            organization_id=organization_id,
            provider_data=provider_data,
            updated_by=current_user.id
        )

        if not provider:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="SSO provider not found"
            )

        return SSOProviderResponse.from_orm(provider)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.delete("/providers/{provider_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_sso_provider(
    provider_id: UUID,
    organization_id: UUID = Query(..., description="Organization ID"),
    db: Session = Depends(get_db),
    current_user: User = Depends(require_admin),
    _: None = Depends(standard_rate_limit)
):
    """
    Delete SSO provider configuration.

    Soft deletes the SSO provider and deactivates all associated
    authentication sessions.
    """
    service = SSOService(db)

    success = await service.delete_sso_provider(
        provider_id=provider_id,
        organization_id=organization_id,
        deleted_by=current_user.id
    )

    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="SSO provider not found"
        )


@router.post("/providers/{provider_id}/test", response_model=SSOTestResult)
async def test_sso_configuration(
    provider_id: UUID,
    test_request: SSOTestRequest,
    organization_id: UUID = Query(..., description="Organization ID"),
    db: Session = Depends(get_db),
    current_user: User = Depends(require_admin),
    _: None = Depends(standard_rate_limit)
):
    """
    Test SSO provider configuration.

    Validates the SSO provider configuration by attempting
    a test authentication flow.
    """
    service = SSOService(db)

    try:
        result = await service.test_sso_provider(
            provider_id=provider_id,
            organization_id=organization_id,
            test_request=test_request
        )
        return result
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/providers/{provider_id}/metadata", response_model=SSOMetadataResponse)
async def get_sso_metadata(
    provider_id: UUID,
    organization_id: UUID = Query(..., description="Organization ID"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    _: None = Depends(standard_rate_limit)
):
    """
    Get SSO metadata for provider configuration.

    Returns the metadata required for configuring the SSO provider
    on the identity provider side.
    """
    service = SSOService(db)

    provider = await service.get_sso_provider(provider_id, organization_id)

    if not provider:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="SSO provider not found"
        )

    # Generate metadata based on provider configuration
    metadata = SSOMetadataResponse(
        entity_id=provider.entity_id or f"urn:regressionrigor:sso:{provider.id}",
        sso_url=provider.sso_url or f"https://app.regressionrigor.com/sso/{provider.id}/login",
        slo_url=provider.slo_url,
        certificate=provider.certificate or "",
        metadata_xml=f"<!-- SAML metadata for provider {provider.name} -->",
        valid_until=None
    )

    return metadata


@router.get("/sessions", response_model=SSOAuthSessionListResponse)
async def get_sso_sessions(
    organization_id: UUID = Query(..., description="Organization ID"),
    provider_id: Optional[UUID] = Query(None, description="Filter by provider ID"),
    user_id: Optional[UUID] = Query(None, description="Filter by user ID"),
    status: Optional[str] = Query(None, description="Filter by session status"),
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    _: None = Depends(standard_rate_limit)
):
    """
    Get SSO authentication sessions.

    Returns a paginated list of SSO authentication sessions
    for monitoring and audit purposes.
    """
    query = db.query(SSOAuthSession).join(SSOProvider).filter(
        SSOProvider.organization_id == organization_id
    )

    # Apply filters
    if provider_id:
        query = query.filter(SSOAuthSession.provider_id == provider_id)

    if user_id:
        query = query.filter(SSOAuthSession.user_id == user_id)

    if status:
        query = query.filter(SSOAuthSession.status == status)

    # Order by most recent first
    query = query.order_by(SSOAuthSession.initiated_at.desc())

    # Paginate results
    result = paginate(query, page=page, size=size)

    return SSOAuthSessionListResponse(
        items=[SSOAuthSessionResponse.from_orm(session) for session in result.items],
        total=result.total,
        page=page,
        size=size,
        pages=result.pages
    )


@router.post("/callback")
async def sso_callback(
    db: Session = Depends(get_db),
    _: None = Depends(standard_rate_limit)
):
    """
    SSO callback endpoint for authentication responses.

    Handles authentication responses from SSO providers
    and completes the authentication flow.
    """
    # TODO: Implement SSO callback handling
    return {"message": "SSO callback endpoint - implementation in progress"}
