"""
API v4 routes for enterprise integration and scalability features.

This module provides the main router for v4 API endpoints including:
- Multi-tenancy and organization management
- Advanced enterprise integrations (SSO, security tools)
- Compliance and governance frameworks
- Advanced scalability and performance management
- Business intelligence and analytics
"""

from fastapi import APIRouter

# Import v4 routers
from api.routes.v4.organizations import router as organizations_router
from api.routes.v4.tenants import router as tenants_router
from api.routes.v4.sso import router as sso_router
from api.routes.v4.integrations import router as integrations_router
from api.routes.v4.compliance import router as compliance_router
from api.routes.v4.audit import router as audit_router
from api.routes.v4.resources import router as resources_router
from api.routes.v4.cache import router as cache_router
from api.routes.v4.bi import router as bi_router
from api.routes.v4.executive import router as executive_router

# Create the v4 router
router = APIRouter()

# Include all v4 routers with appropriate prefixes and tags
router.include_router(
    organizations_router,
    prefix="/organizations",
    tags=["Organizations"]
)

router.include_router(
    tenants_router,
    prefix="/tenants",
    tags=["Tenant Management"]
)

router.include_router(
    sso_router,
    prefix="/sso",
    tags=["Single Sign-On"]
)

router.include_router(
    integrations_router,
    prefix="/integrations",
    tags=["Enterprise Integrations"]
)

router.include_router(
    compliance_router,
    prefix="/compliance",
    tags=["Compliance & Governance"]
)

router.include_router(
    audit_router,
    prefix="/audit",
    tags=["Audit & Governance"]
)

router.include_router(
    resources_router,
    prefix="/resources",
    tags=["Resource Management"]
)

router.include_router(
    cache_router,
    prefix="/cache",
    tags=["Cache Management"]
)

router.include_router(
    bi_router,
    prefix="/bi",
    tags=["Business Intelligence"]
)

router.include_router(
    executive_router,
    prefix="/executive",
    tags=["Executive Dashboard"]
)
