"""Email utility functions.

This module provides utility functions for sending emails, including
formatting and sending HTML and text emails with attachments.
"""
import os
import smtplib
from datetime import datetime
from email.mime.application import MIMEApplication
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from typing import List, Optional, Union

from jinja2 import Template

from api.config import settings
from api.utils.logging_config import get_logger

logger = get_logger(__name__)


def send_email(
    recipients: List[str],
    subject: str,
    body_html: Optional[str] = None,
    body_text: Optional[str] = None,
    attachments: Optional[List[str]] = None,
    cc: Optional[List[str]] = None,
    bcc: Optional[List[str]] = None,
    sender: Optional[str] = None,
) -> bool:
    """Send an email.

    Args:
        recipients: List of recipient email addresses
        subject: Email subject
        body_html: HTML body of the email (optional)
        body_text: Text body of the email (optional)
        attachments: List of file paths to attach (optional)
        cc: List of CC email addresses (optional)
        bcc: List of BCC email addresses (optional)
        sender: Sender email address (optional, defaults to configured sender)

    Returns:
        True if the email was sent successfully, False otherwise
    """
    if not body_html and not body_text:
        raise ValueError("Either body_html or body_text must be provided")

    # Use configured sender if not provided
    if not sender:
        sender = settings.EMAIL_SENDER

    # Create message
    msg = MIMEMultipart("alternative")
    msg["Subject"] = subject
    msg["From"] = sender
    msg["To"] = ", ".join(recipients)

    if cc:
        msg["Cc"] = ", ".join(cc)
    if bcc:
        msg["Bcc"] = ", ".join(bcc)

    # Attach text and HTML parts
    if body_text:
        msg.attach(MIMEText(body_text, "plain"))
    if body_html:
        msg.attach(MIMEText(body_html, "html"))

    # Attach files
    if attachments:
        for file_path in attachments:
            if not os.path.isfile(file_path):
                continue

            with open(file_path, "rb") as file:
                part = MIMEApplication(file.read(), Name=os.path.basename(file_path))
                part["Content-Disposition"] = f'attachment; filename="{os.path.basename(file_path)}"'
                msg.attach(part)

    # Send email
    try:
        with smtplib.SMTP(settings.SMTP_SERVER, settings.SMTP_PORT) as server:
            if settings.SMTP_USE_TLS:
                server.starttls()
            if settings.SMTP_USERNAME and settings.SMTP_PASSWORD:
                server.login(settings.SMTP_USERNAME, settings.SMTP_PASSWORD)

            all_recipients = recipients.copy()
            if cc:
                all_recipients.extend(cc)
            if bcc:
                all_recipients.extend(bcc)

            server.sendmail(sender, all_recipients, msg.as_string())
        return True
    except Exception as e:
        print(f"Error sending email: {e}")
        return False


def render_template(
    template_str: str,
    context: dict,
) -> str:
    """Render a template string with the given context.

    Args:
        template_str: Template string to render
        context: Context variables for the template

    Returns:
        Rendered template string
    """
    template = Template(template_str)
    return template.render(**context)


def send_report_email(
    recipients: List[str],
    subject: str,
    body_template: str,
    context: dict,
    report_file_path: str,
    cc: Optional[List[str]] = None,
    bcc: Optional[List[str]] = None,
) -> bool:
    """Send a report email.

    Args:
        recipients: List of recipient email addresses
        subject: Email subject
        body_template: Template for the email body
        context: Context variables for the template
        report_file_path: Path to the report file to attach
        cc: List of CC email addresses (optional)
        bcc: List of BCC email addresses (optional)

    Returns:
        True if the email was sent successfully, False otherwise
    """
    # Render body template
    body_html = render_template(body_template, context)

    # Send email with attachment
    return send_email(
        recipients=recipients,
        subject=subject,
        body_html=body_html,
        attachments=[report_file_path],
        cc=cc,
        bcc=bcc,
    )


async def send_invitation_email(
    email: str,
    organization_name: str,
    invitation_token: str,
    expires_at: datetime
) -> bool:
    """
    Send organization invitation email.

    Args:
        email: Recipient email address
        organization_name: Name of the organization
        invitation_token: Invitation token for acceptance
        expires_at: Expiration date of the invitation

    Returns:
        True if email sent successfully, False otherwise
    """
    try:
        # Create invitation URL
        invitation_url = f"{settings.FRONTEND_URL}/invitations/accept?token={invitation_token}"

        # Create email content
        subject = f"Invitation to join {organization_name}"

        html_content = f"""
        <html>
        <body>
            <h2>You're invited to join {organization_name}</h2>
            <p>You have been invited to join the organization <strong>{organization_name}</strong> on RegressionRigor.</p>

            <p>To accept this invitation, please click the link below:</p>
            <p><a href="{invitation_url}" style="background-color: #4CAF50; color: white; padding: 14px 20px; text-decoration: none; border-radius: 4px;">Accept Invitation</a></p>

            <p>Or copy and paste this URL into your browser:</p>
            <p>{invitation_url}</p>

            <p><strong>Important:</strong> This invitation expires on {expires_at.strftime('%B %d, %Y at %I:%M %p UTC')}.</p>

            <p>If you did not expect this invitation, you can safely ignore this email.</p>

            <hr>
            <p><small>This email was sent by RegressionRigor. If you have any questions, please contact our support team.</small></p>
        </body>
        </html>
        """

        text_content = f"""
        You're invited to join {organization_name}

        You have been invited to join the organization {organization_name} on RegressionRigor.

        To accept this invitation, please visit:
        {invitation_url}

        This invitation expires on {expires_at.strftime('%B %d, %Y at %I:%M %p UTC')}.

        If you did not expect this invitation, you can safely ignore this email.
        """

        # Send email using existing function
        success = send_email(
            recipients=[email],
            subject=subject,
            body_html=html_content,
            body_text=text_content
        )

        if success:
            logger.info(f"Invitation email sent successfully to {email}")
        else:
            logger.error(f"Failed to send invitation email to {email}")

        return success

    except Exception as e:
        logger.error(f"Error sending invitation email to {email}: {e}")
        return False
