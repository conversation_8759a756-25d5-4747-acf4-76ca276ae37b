"""
Pydantic schemas for organization management and multi-tenancy.

This module defines the request/response schemas for organization
management, tenant isolation, and subscription management APIs.
"""

from datetime import datetime
from typing import Dict, Any, List, Optional
from uuid import UUID
from enum import Enum

from pydantic import BaseModel, Field, EmailStr, validator


class SubscriptionTier(str, Enum):
    """Subscription tier enumeration."""
    STARTER = "starter"
    PROFESSIONAL = "professional"
    ENTERPRISE = "enterprise"
    CUSTOM = "custom"


class ResourceType(str, Enum):
    """Resource type enumeration."""
    COMPUTE = "compute"
    STORAGE = "storage"
    NETWORK = "network"
    DATABASE = "database"
    CACHE = "cache"


class InvitationStatus(str, Enum):
    """Invitation status enumeration."""
    PENDING = "pending"
    ACCEPTED = "accepted"
    DECLINED = "declined"
    EXPIRED = "expired"


# Organization Settings Schemas
class PasswordPolicy(BaseModel):
    """Password policy configuration."""
    min_length: int = Field(8, ge=8, le=128)
    require_uppercase: bool = True
    require_lowercase: bool = True
    require_numbers: bool = True
    require_special_chars: bool = True
    max_age_days: int = Field(90, ge=30, le=365)


class SecuritySettings(BaseModel):
    """Security settings for organization."""
    enforce_mfa: bool = True
    session_timeout_minutes: int = Field(480, ge=30, le=1440)  # 8 hours default
    password_policy: PasswordPolicy
    ip_whitelist: List[str] = Field(default_factory=list)
    sso_required: bool = False
    audit_log_retention_days: int = Field(2555, ge=365, le=2555)  # 7 years


class BrandingSettings(BaseModel):
    """Branding settings for organization."""
    logo_url: Optional[str] = None
    primary_color: Optional[str] = None
    secondary_color: Optional[str] = None
    custom_css: Optional[str] = None


class OrganizationSettings(BaseModel):
    """Organization settings configuration."""
    max_users: int = Field(100, ge=1, le=10000)
    max_campaigns: int = Field(50, ge=1, le=1000)
    max_assessments: int = Field(200, ge=1, le=5000)
    data_retention_days: int = Field(365, ge=30, le=2555)  # 7 years max
    allowed_integrations: List[str] = Field(default_factory=list)
    security_settings: SecuritySettings
    branding: Optional[BrandingSettings] = None


class ContactInfo(BaseModel):
    """Contact information for organization."""
    primary_email: EmailStr
    phone: Optional[str] = None
    address: Optional[str] = None
    city: Optional[str] = None
    state: Optional[str] = None
    country: Optional[str] = None
    postal_code: Optional[str] = None


class ComplianceRequirement(BaseModel):
    """Compliance requirement specification."""
    framework: str  # e.g., "SOC2", "ISO27001", "HIPAA"
    version: str
    required_controls: List[str] = Field(default_factory=list)
    audit_frequency: str = "annual"  # annual, semi-annual, quarterly


# Organization CRUD Schemas
class OrganizationCreate(BaseModel):
    """Schema for creating a new organization."""
    name: str = Field(..., min_length=1, max_length=255)
    display_name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = Field(None, max_length=1000)
    settings: OrganizationSettings
    subscription_tier: SubscriptionTier = SubscriptionTier.STARTER
    contact_info: ContactInfo
    compliance_requirements: List[ComplianceRequirement] = Field(default_factory=list)


class OrganizationUpdate(BaseModel):
    """Schema for updating an organization."""
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    display_name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = Field(None, max_length=1000)
    settings: Optional[OrganizationSettings] = None
    subscription_tier: Optional[SubscriptionTier] = None
    contact_info: Optional[ContactInfo] = None
    compliance_requirements: Optional[List[ComplianceRequirement]] = None
    is_active: Optional[bool] = None


class OrganizationResponse(BaseModel):
    """Schema for organization response."""
    id: UUID
    name: str
    display_name: str
    description: Optional[str]
    subscription_tier: SubscriptionTier
    settings: Dict[str, Any]
    contact_info: Dict[str, Any]
    compliance_requirements: List[Dict[str, Any]]
    is_active: bool
    is_trial: bool
    trial_expires_at: Optional[datetime]
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


# Resource Management Schemas
class ResourceLimits(BaseModel):
    """Resource limits configuration."""
    cpu_cores: Optional[int] = None
    memory_gb: Optional[int] = None
    storage_gb: Optional[int] = None
    network_bandwidth_mbps: Optional[int] = None
    api_requests_per_hour: Optional[int] = None


class TenantResourceCreate(BaseModel):
    """Schema for creating tenant resource allocation."""
    resource_type: ResourceType
    resource_id: UUID
    usage_metrics: Dict[str, Any] = Field(default_factory=dict)
    limits: ResourceLimits


class TenantResourceResponse(BaseModel):
    """Schema for tenant resource response."""
    id: UUID
    tenant_id: UUID
    resource_type: ResourceType
    resource_id: UUID
    allocated_at: datetime
    usage_metrics: Dict[str, Any]
    limits: Dict[str, Any]
    is_active: bool
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


# Invitation Schemas
class OrganizationInvitationCreate(BaseModel):
    """Schema for creating organization invitation."""
    email: EmailStr
    role: str = Field("member", min_length=1, max_length=50)
    expires_in_days: int = Field(7, ge=1, le=30)


class OrganizationInvitationResponse(BaseModel):
    """Schema for organization invitation response."""
    id: UUID
    organization_id: UUID
    email: str
    role: str
    status: InvitationStatus
    expires_at: datetime
    accepted_at: Optional[datetime]
    created_at: datetime
    
    class Config:
        from_attributes = True


# Usage Tracking Schemas
class OrganizationUsageResponse(BaseModel):
    """Schema for organization usage response."""
    id: UUID
    organization_id: UUID
    period_start: datetime
    period_end: datetime
    active_users: int
    api_requests: int
    storage_used_gb: float
    compute_hours: float
    detailed_metrics: Dict[str, Any]
    billable_amount: Optional[float]
    currency: str
    
    class Config:
        from_attributes = True


# List Response Schemas
class OrganizationListResponse(BaseModel):
    """Schema for paginated organization list response."""
    items: List[OrganizationResponse]
    total: int
    page: int
    size: int
    pages: int


class TenantResourceListResponse(BaseModel):
    """Schema for paginated tenant resource list response."""
    items: List[TenantResourceResponse]
    total: int
    page: int
    size: int
    pages: int
