"""
Pydantic schemas for SSO provider management.

This module defines the request/response schemas for SSO provider
configuration, authentication sessions, and attribute mapping.
"""

from datetime import datetime
from typing import Dict, Any, List, Optional
from uuid import UUID
from enum import Enum

from pydantic import BaseModel, Field, HttpUrl, validator


class SSOProviderType(str, Enum):
    """SSO provider type enumeration."""
    SAML2 = "saml2"
    OIDC = "oidc"
    OAUTH2 = "oauth2"
    LDAP = "ldap"
    ACTIVE_DIRECTORY = "active_directory"


class SSOProviderStatus(str, Enum):
    """SSO provider status enumeration."""
    DRAFT = "draft"
    ACTIVE = "active"
    INACTIVE = "inactive"
    ERROR = "error"
    TESTING = "testing"


class SSOAuthStatus(str, Enum):
    """SSO authentication status enumeration."""
    INITIATED = "initiated"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    EXPIRED = "expired"


# SAML2 Configuration Schemas
class SAML2Configuration(BaseModel):
    """SAML2 provider configuration."""
    entity_id: str = Field(..., description="SAML2 entity ID")
    sso_url: HttpUrl = Field(..., description="SAML2 SSO URL")
    slo_url: Optional[HttpUrl] = Field(None, description="SAML2 SLO URL")
    certificate: str = Field(..., description="X.509 certificate")
    name_id_format: str = Field("urn:oasis:names:tc:SAML:2.0:nameid-format:persistent", description="NameID format")
    want_assertions_signed: bool = Field(True, description="Require signed assertions")
    want_response_signed: bool = Field(True, description="Require signed responses")
    signature_algorithm: str = Field("http://www.w3.org/2001/04/xmldsig-more#rsa-sha256", description="Signature algorithm")


# OIDC Configuration Schemas
class OIDCConfiguration(BaseModel):
    """OIDC provider configuration."""
    issuer: HttpUrl = Field(..., description="OIDC issuer URL")
    client_id: str = Field(..., description="OIDC client ID")
    client_secret: str = Field(..., description="OIDC client secret")
    authorization_endpoint: HttpUrl = Field(..., description="Authorization endpoint")
    token_endpoint: HttpUrl = Field(..., description="Token endpoint")
    userinfo_endpoint: HttpUrl = Field(..., description="UserInfo endpoint")
    jwks_uri: Optional[HttpUrl] = Field(None, description="JWKS URI")
    scopes: List[str] = Field(["openid", "profile", "email"], description="OIDC scopes")


# OAuth2 Configuration Schemas
class OAuth2Configuration(BaseModel):
    """OAuth2 provider configuration."""
    client_id: str = Field(..., description="OAuth2 client ID")
    client_secret: str = Field(..., description="OAuth2 client secret")
    authorization_endpoint: HttpUrl = Field(..., description="Authorization endpoint")
    token_endpoint: HttpUrl = Field(..., description="Token endpoint")
    user_info_endpoint: HttpUrl = Field(..., description="User info endpoint")
    scopes: List[str] = Field(["profile", "email"], description="OAuth2 scopes")


# LDAP Configuration Schemas
class LDAPConfiguration(BaseModel):
    """LDAP provider configuration."""
    server_url: str = Field(..., description="LDAP server URL")
    bind_dn: str = Field(..., description="Bind DN")
    bind_password: str = Field(..., description="Bind password")
    base_dn: str = Field(..., description="Base DN for user search")
    user_filter: str = Field("(uid={username})", description="User search filter")
    user_attributes: List[str] = Field(["uid", "cn", "mail"], description="User attributes to retrieve")
    use_ssl: bool = Field(True, description="Use SSL/TLS")
    use_start_tls: bool = Field(False, description="Use StartTLS")


# Active Directory Configuration Schemas
class ActiveDirectoryConfiguration(BaseModel):
    """Active Directory provider configuration."""
    domain: str = Field(..., description="AD domain")
    server: str = Field(..., description="AD server")
    bind_user: str = Field(..., description="Bind user")
    bind_password: str = Field(..., description="Bind password")
    base_dn: str = Field(..., description="Base DN")
    user_filter: str = Field("(sAMAccountName={username})", description="User filter")
    group_filter: str = Field("(member={user_dn})", description="Group filter")
    use_ssl: bool = Field(True, description="Use SSL")


# Attribute Mapping Schemas
class AttributeMapping(BaseModel):
    """Attribute mapping configuration."""
    email: str = Field("email", description="Email attribute mapping")
    first_name: str = Field("given_name", description="First name attribute mapping")
    last_name: str = Field("family_name", description="Last name attribute mapping")
    username: str = Field("preferred_username", description="Username attribute mapping")
    groups: Optional[str] = Field(None, description="Groups attribute mapping")
    department: Optional[str] = Field(None, description="Department attribute mapping")
    title: Optional[str] = Field(None, description="Title attribute mapping")


# SSO Provider CRUD Schemas
class SSOProviderCreate(BaseModel):
    """Schema for creating SSO provider."""
    name: str = Field(..., min_length=1, max_length=255)
    display_name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = Field(None, max_length=1000)
    provider_type: SSOProviderType
    configuration: Dict[str, Any] = Field(..., description="Provider-specific configuration")
    attribute_mapping: AttributeMapping = Field(default_factory=AttributeMapping)
    auto_provision_users: bool = Field(False, description="Auto-provision users on first login")
    require_encrypted_assertions: bool = Field(True, description="Require encrypted assertions")
    
    @validator('configuration')
    def validate_configuration(cls, v, values):
        """Validate configuration based on provider type."""
        provider_type = values.get('provider_type')
        
        if provider_type == SSOProviderType.SAML2:
            SAML2Configuration(**v)
        elif provider_type == SSOProviderType.OIDC:
            OIDCConfiguration(**v)
        elif provider_type == SSOProviderType.OAUTH2:
            OAuth2Configuration(**v)
        elif provider_type == SSOProviderType.LDAP:
            LDAPConfiguration(**v)
        elif provider_type == SSOProviderType.ACTIVE_DIRECTORY:
            ActiveDirectoryConfiguration(**v)
        
        return v


class SSOProviderUpdate(BaseModel):
    """Schema for updating SSO provider."""
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    display_name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = Field(None, max_length=1000)
    configuration: Optional[Dict[str, Any]] = None
    attribute_mapping: Optional[AttributeMapping] = None
    status: Optional[SSOProviderStatus] = None
    auto_provision_users: Optional[bool] = None
    require_encrypted_assertions: Optional[bool] = None


class SSOProviderResponse(BaseModel):
    """Schema for SSO provider response."""
    id: UUID
    organization_id: UUID
    name: str
    display_name: str
    description: Optional[str]
    provider_type: SSOProviderType
    status: SSOProviderStatus
    configuration: Dict[str, Any]
    attribute_mapping: Dict[str, Any]
    is_default: bool
    auto_provision_users: bool
    require_encrypted_assertions: bool
    last_test_date: Optional[datetime]
    last_test_result: Optional[Dict[str, Any]]
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


# Authentication Session Schemas
class SSOAuthSessionResponse(BaseModel):
    """Schema for SSO authentication session response."""
    id: UUID
    provider_id: UUID
    user_id: Optional[UUID]
    session_id: str
    status: SSOAuthStatus
    initiated_at: datetime
    completed_at: Optional[datetime]
    sso_user_id: Optional[str]
    sso_attributes: Optional[Dict[str, Any]]
    error_code: Optional[str]
    error_message: Optional[str]
    client_ip: Optional[str]
    
    class Config:
        from_attributes = True


# Test Configuration Schemas
class SSOTestRequest(BaseModel):
    """Schema for SSO provider test request."""
    test_type: str = Field("connection", pattern="^(connection|authentication|metadata)$")
    test_user: Optional[str] = Field(None, description="Test username for authentication test")
    test_password: Optional[str] = Field(None, description="Test password for authentication test")


class SSOTestResult(BaseModel):
    """Schema for SSO provider test result."""
    success: bool
    test_type: str
    message: str
    details: Optional[Dict[str, Any]] = None
    tested_at: datetime
    duration_ms: int


# Metadata Schemas
class SSOMetadataResponse(BaseModel):
    """Schema for SSO metadata response."""
    entity_id: str
    sso_url: str
    slo_url: Optional[str]
    certificate: str
    metadata_xml: str
    valid_until: Optional[datetime]


# List Response Schemas
class SSOProviderListResponse(BaseModel):
    """Schema for paginated SSO provider list response."""
    items: List[SSOProviderResponse]
    total: int
    page: int
    size: int
    pages: int


class SSOAuthSessionListResponse(BaseModel):
    """Schema for paginated SSO auth session list response."""
    items: List[SSOAuthSessionResponse]
    total: int
    page: int
    size: int
    pages: int
