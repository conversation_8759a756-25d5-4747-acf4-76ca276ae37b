"""
Pydantic schemas for enterprise security tool integrations.

This module defines the request/response schemas for enterprise
integration management, sync jobs, and data mapping.
"""

from datetime import datetime
from typing import Dict, Any, List, Optional
from uuid import UUID
from enum import Enum

from pydantic import BaseModel, Field, HttpUrl, validator


class IntegrationType(str, Enum):
    """Integration type enumeration."""
    SIEM = "siem"
    SOAR = "soar"
    TIP = "tip"
    VULNERABILITY_SCANNER = "vulnerability_scanner"
    EDR = "edr"
    NETWORK_SCANNER = "network_scanner"
    COMPLIANCE_TOOL = "compliance_tool"
    TICKETING_SYSTEM = "ticketing_system"
    NOTIFICATION_SYSTEM = "notification_system"


class IntegrationStatus(str, Enum):
    """Integration status enumeration."""
    DRAFT = "draft"
    ACTIVE = "active"
    INACTIVE = "inactive"
    ERROR = "error"
    TESTING = "testing"
    SYNCING = "syncing"


class SyncStatus(str, Enum):
    """Sync status enumeration."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class SyncJobType(str, Enum):
    """Sync job type enumeration."""
    FULL_SYNC = "full_sync"
    INCREMENTAL_SYNC = "incremental_sync"
    EXPORT = "export"
    IMPORT = "import"
    TEST = "test"


class MappingDirection(str, Enum):
    """Mapping direction enumeration."""
    INBOUND = "inbound"
    OUTBOUND = "outbound"
    BIDIRECTIONAL = "bidirectional"


class EventType(str, Enum):
    """Integration event type enumeration."""
    CONNECTION_TEST = "connection_test"
    SYNC_STARTED = "sync_started"
    SYNC_COMPLETED = "sync_completed"
    SYNC_FAILED = "sync_failed"
    DATA_RECEIVED = "data_received"
    DATA_SENT = "data_sent"
    ERROR = "error"
    WARNING = "warning"
    INFO = "info"


class EventSeverity(str, Enum):
    """Event severity enumeration."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


# Connection Configuration Schemas
class HTTPConnectionConfig(BaseModel):
    """HTTP connection configuration."""
    base_url: HttpUrl = Field(..., description="Base URL for the API")
    timeout_seconds: int = Field(30, ge=1, le=300, description="Request timeout")
    verify_ssl: bool = Field(True, description="Verify SSL certificates")
    headers: Dict[str, str] = Field(default_factory=dict, description="Default headers")


class DatabaseConnectionConfig(BaseModel):
    """Database connection configuration."""
    host: str = Field(..., description="Database host")
    port: int = Field(..., ge=1, le=65535, description="Database port")
    database: str = Field(..., description="Database name")
    username: str = Field(..., description="Database username")
    password: str = Field(..., description="Database password")
    ssl_mode: str = Field("require", description="SSL mode")


# Authentication Configuration Schemas
class APIKeyAuthConfig(BaseModel):
    """API key authentication configuration."""
    api_key: str = Field(..., description="API key")
    header_name: str = Field("X-API-Key", description="Header name for API key")


class OAuth2AuthConfig(BaseModel):
    """OAuth2 authentication configuration."""
    client_id: str = Field(..., description="OAuth2 client ID")
    client_secret: str = Field(..., description="OAuth2 client secret")
    token_url: HttpUrl = Field(..., description="Token endpoint URL")
    scopes: List[str] = Field(default_factory=list, description="OAuth2 scopes")


class BasicAuthConfig(BaseModel):
    """Basic authentication configuration."""
    username: str = Field(..., description="Username")
    password: str = Field(..., description="Password")


# Data Mapping Schemas
class DataMappingRule(BaseModel):
    """Data mapping rule configuration."""
    source_field: str = Field(..., description="Source field name")
    target_field: str = Field(..., description="Target field name")
    field_type: str = Field(..., pattern="^(string|integer|boolean|date|json)$")
    is_required: bool = Field(False, description="Whether field is required")
    default_value: Optional[str] = Field(None, description="Default value if source is empty")
    transformation_rules: Optional[Dict[str, Any]] = Field(None, description="Transformation rules")
    validation_rules: Optional[Dict[str, Any]] = Field(None, description="Validation rules")
    direction: MappingDirection = Field(MappingDirection.BIDIRECTIONAL, description="Mapping direction")


# Sync Configuration Schemas
class SyncConfig(BaseModel):
    """Sync configuration."""
    auto_sync: bool = Field(False, description="Enable automatic synchronization")
    sync_interval_minutes: int = Field(60, ge=1, le=10080, description="Sync interval in minutes")
    batch_size: int = Field(100, ge=1, le=10000, description="Batch size for sync operations")
    retry_attempts: int = Field(3, ge=0, le=10, description="Number of retry attempts")
    retry_delay_seconds: int = Field(60, ge=1, le=3600, description="Delay between retries")


# Integration CRUD Schemas
class EnterpriseIntegrationCreate(BaseModel):
    """Schema for creating enterprise integration."""
    name: str = Field(..., min_length=1, max_length=255)
    display_name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = Field(None, max_length=1000)
    integration_type: IntegrationType
    vendor: str = Field(..., min_length=1, max_length=255)
    product: str = Field(..., min_length=1, max_length=255)
    version: Optional[str] = Field(None, max_length=100)
    connection_config: Dict[str, Any] = Field(..., description="Connection configuration")
    auth_config: Dict[str, Any] = Field(..., description="Authentication configuration")
    data_mapping: List[DataMappingRule] = Field(default_factory=list, description="Data mapping rules")
    sync_config: SyncConfig = Field(default_factory=SyncConfig, description="Sync configuration")


class EnterpriseIntegrationUpdate(BaseModel):
    """Schema for updating enterprise integration."""
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    display_name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = Field(None, max_length=1000)
    vendor: Optional[str] = Field(None, min_length=1, max_length=255)
    product: Optional[str] = Field(None, min_length=1, max_length=255)
    version: Optional[str] = Field(None, max_length=100)
    connection_config: Optional[Dict[str, Any]] = None
    auth_config: Optional[Dict[str, Any]] = None
    data_mapping: Optional[List[DataMappingRule]] = None
    sync_config: Optional[SyncConfig] = None
    status: Optional[IntegrationStatus] = None
    is_enabled: Optional[bool] = None


class EnterpriseIntegrationResponse(BaseModel):
    """Schema for enterprise integration response."""
    id: UUID
    organization_id: UUID
    name: str
    display_name: str
    description: Optional[str]
    integration_type: IntegrationType
    status: IntegrationStatus
    vendor: str
    product: str
    version: Optional[str]
    connection_config: Dict[str, Any]
    auth_config: Dict[str, Any]  # Sensitive data should be masked
    data_mapping: Dict[str, Any]
    sync_config: Dict[str, Any]
    is_enabled: bool
    auto_sync: bool
    sync_interval_minutes: int
    last_test_date: Optional[datetime]
    last_test_result: Optional[Dict[str, Any]]
    last_sync_date: Optional[datetime]
    last_sync_result: Optional[Dict[str, Any]]
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


# Sync Job Schemas
class SyncJobCreate(BaseModel):
    """Schema for creating sync job."""
    job_type: SyncJobType = Field(SyncJobType.INCREMENTAL_SYNC, description="Type of sync job")
    sync_parameters: Optional[Dict[str, Any]] = Field(None, description="Sync parameters")
    scheduled_at: Optional[datetime] = Field(None, description="When to run the job")


class SyncJobResponse(BaseModel):
    """Schema for sync job response."""
    id: UUID
    integration_id: UUID
    job_type: SyncJobType
    status: SyncStatus
    started_at: Optional[datetime]
    completed_at: Optional[datetime]
    scheduled_at: datetime
    total_records: Optional[int]
    processed_records: int
    success_records: int
    error_records: int
    result_summary: Optional[Dict[str, Any]]
    error_details: Optional[Dict[str, Any]]
    sync_parameters: Optional[Dict[str, Any]]
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


# Integration Test Schemas
class IntegrationTestRequest(BaseModel):
    """Schema for integration test request."""
    test_type: str = Field("connection", pattern="^(connection|authentication|data_mapping|sync)$")
    test_parameters: Optional[Dict[str, Any]] = Field(None, description="Test parameters")


class IntegrationTestResult(BaseModel):
    """Schema for integration test result."""
    success: bool
    test_type: str
    message: str
    details: Optional[Dict[str, Any]] = None
    tested_at: datetime
    duration_ms: int


# Event Schemas
class IntegrationEventResponse(BaseModel):
    """Schema for integration event response."""
    id: UUID
    integration_id: UUID
    sync_job_id: Optional[UUID]
    event_type: EventType
    severity: EventSeverity
    message: str
    details: Optional[Dict[str, Any]]
    user_id: Optional[UUID]
    source_ip: Optional[str]
    created_at: datetime
    
    class Config:
        from_attributes = True


# List Response Schemas
class EnterpriseIntegrationListResponse(BaseModel):
    """Schema for paginated enterprise integration list response."""
    items: List[EnterpriseIntegrationResponse]
    total: int
    page: int
    size: int
    pages: int


class SyncJobListResponse(BaseModel):
    """Schema for paginated sync job list response."""
    items: List[SyncJobResponse]
    total: int
    page: int
    size: int
    pages: int


class IntegrationEventListResponse(BaseModel):
    """Schema for paginated integration event list response."""
    items: List[IntegrationEventResponse]
    total: int
    page: int
    size: int
    pages: int
