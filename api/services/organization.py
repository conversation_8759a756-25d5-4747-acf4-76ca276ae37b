"""
Organization management service for multi-tenancy operations.

This module provides business logic for organization management,
including CRUD operations, user management, and resource allocation.
"""

import secrets
from datetime import datetime, timedelta
from typing import Optional, List
from uuid import UUID

from sqlalchemy.orm import Session
from sqlalchemy import and_, or_

from api.models.organization import (
    Organization, 
    OrganizationInvitation, 
    TenantResource,
    OrganizationUsage
)
from api.models.user import User
from api.schemas.organization import (
    OrganizationCreate,
    OrganizationUpdate,
    OrganizationInvitationCreate,
    TenantResourceCreate
)
from api.utils.email import send_invitation_email
from api.utils.logging_config import get_logger

logger = get_logger(__name__)


class OrganizationService:
    """Service class for organization management operations."""
    
    def __init__(self, db: Session):
        self.db = db
    
    async def create_organization(
        self, 
        organization_data: OrganizationCreate,
        owner_id: UUID
    ) -> Organization:
        """
        Create a new organization with the specified owner.
        
        Args:
            organization_data: Organization creation data
            owner_id: ID of the user who will own the organization
            
        Returns:
            Created organization instance
            
        Raises:
            ValueError: If organization name already exists or data is invalid
        """
        # Check if organization name already exists
        existing_org = self.db.query(Organization).filter(
            and_(
                Organization.name == organization_data.name,
                Organization.deleted_at.is_(None)
            )
        ).first()
        
        if existing_org:
            raise ValueError(f"Organization with name '{organization_data.name}' already exists")
        
        # Create organization
        organization = Organization(
            name=organization_data.name,
            display_name=organization_data.display_name,
            description=organization_data.description,
            subscription_tier=organization_data.subscription_tier,
            settings=organization_data.settings.dict(),
            contact_info=organization_data.contact_info.dict(),
            compliance_requirements=[req.dict() for req in organization_data.compliance_requirements],
            is_active=True,
            is_trial=organization_data.subscription_tier == "starter"
        )
        
        # Set trial expiration for starter tier
        if organization.is_trial:
            organization.trial_expires_at = datetime.utcnow() + timedelta(days=30)
        
        self.db.add(organization)
        self.db.commit()
        self.db.refresh(organization)
        
        logger.info(f"Created organization {organization.id} with owner {owner_id}")
        
        return organization
    
    async def get_organization(
        self, 
        organization_id: UUID,
        user_id: UUID
    ) -> Optional[Organization]:
        """
        Get organization by ID with user access validation.
        
        Args:
            organization_id: Organization ID
            user_id: User ID requesting access
            
        Returns:
            Organization instance if found and accessible, None otherwise
        """
        organization = self.db.query(Organization).filter(
            and_(
                Organization.id == organization_id,
                Organization.deleted_at.is_(None)
            )
        ).first()
        
        if not organization:
            return None
        
        # TODO: Implement user-organization access validation
        # For now, return the organization if it exists
        
        return organization
    
    async def update_organization(
        self,
        organization_id: UUID,
        organization_data: OrganizationUpdate,
        user_id: UUID
    ) -> Optional[Organization]:
        """
        Update organization with new data.
        
        Args:
            organization_id: Organization ID to update
            organization_data: Updated organization data
            user_id: User ID performing the update
            
        Returns:
            Updated organization instance if successful, None if not found
            
        Raises:
            PermissionError: If user lacks permission to update organization
            ValueError: If update data is invalid
        """
        organization = await self.get_organization(organization_id, user_id)
        
        if not organization:
            return None
        
        # TODO: Implement permission checking
        # For now, allow all updates
        
        # Update fields if provided
        update_data = organization_data.dict(exclude_unset=True)
        
        for field, value in update_data.items():
            if field == "settings" and value:
                organization.settings = value.dict()
            elif field == "contact_info" and value:
                organization.contact_info = value.dict()
            elif field == "compliance_requirements" and value:
                organization.compliance_requirements = [req.dict() for req in value]
            else:
                setattr(organization, field, value)
        
        organization.updated_at = datetime.utcnow()
        
        self.db.commit()
        self.db.refresh(organization)
        
        logger.info(f"Updated organization {organization_id} by user {user_id}")
        
        return organization
    
    async def delete_organization(
        self,
        organization_id: UUID,
        user_id: UUID
    ) -> bool:
        """
        Soft delete an organization.
        
        Args:
            organization_id: Organization ID to delete
            user_id: User ID performing the deletion
            
        Returns:
            True if successful, False if organization not found
            
        Raises:
            PermissionError: If user lacks permission to delete organization
        """
        organization = await self.get_organization(organization_id, user_id)
        
        if not organization:
            return False
        
        # TODO: Implement permission checking for organization owner
        # For now, allow all deletions
        
        # Soft delete the organization
        organization.deleted_at = datetime.utcnow()
        organization.is_active = False
        
        self.db.commit()
        
        logger.info(f"Deleted organization {organization_id} by user {user_id}")
        
        return True
    
    async def invite_user(
        self,
        organization_id: UUID,
        invitation_data: OrganizationInvitationCreate,
        invited_by: UUID
    ) -> OrganizationInvitation:
        """
        Create an invitation for a user to join the organization.
        
        Args:
            organization_id: Organization ID
            invitation_data: Invitation details
            invited_by: User ID who is sending the invitation
            
        Returns:
            Created invitation instance
            
        Raises:
            ValueError: If invitation data is invalid
            PermissionError: If user lacks permission to invite
        """
        organization = await self.get_organization(organization_id, invited_by)
        
        if not organization:
            raise ValueError("Organization not found")
        
        # Check if user is already invited or is a member
        existing_invitation = self.db.query(OrganizationInvitation).filter(
            and_(
                OrganizationInvitation.organization_id == organization_id,
                OrganizationInvitation.email == invitation_data.email,
                OrganizationInvitation.status == "pending"
            )
        ).first()
        
        if existing_invitation:
            raise ValueError("User already has a pending invitation")
        
        # Generate invitation token
        token = secrets.token_urlsafe(32)
        
        # Create invitation
        invitation = OrganizationInvitation(
            organization_id=organization_id,
            email=invitation_data.email,
            role=invitation_data.role,
            invited_by=invited_by,
            token=token,
            expires_at=datetime.utcnow() + timedelta(days=invitation_data.expires_in_days)
        )
        
        self.db.add(invitation)
        self.db.commit()
        self.db.refresh(invitation)
        
        # Send invitation email (async)
        try:
            await send_invitation_email(
                email=invitation_data.email,
                organization_name=organization.display_name,
                invitation_token=token,
                expires_at=invitation.expires_at
            )
        except Exception as e:
            logger.error(f"Failed to send invitation email: {e}")
            # Don't fail the invitation creation if email fails
        
        logger.info(f"Created invitation {invitation.id} for {invitation_data.email}")
        
        return invitation
    
    async def remove_user(
        self,
        organization_id: UUID,
        user_id: UUID,
        removed_by: UUID
    ) -> bool:
        """
        Remove a user from the organization.
        
        Args:
            organization_id: Organization ID
            user_id: User ID to remove
            removed_by: User ID performing the removal
            
        Returns:
            True if successful, False if user not found in organization
            
        Raises:
            PermissionError: If user lacks permission to remove users
        """
        organization = await self.get_organization(organization_id, removed_by)
        
        if not organization:
            return False
        
        # TODO: Implement user-organization relationship removal
        # This would involve removing the user from organization membership
        # and revoking associated permissions
        
        logger.info(f"Removed user {user_id} from organization {organization_id}")
        
        return True
