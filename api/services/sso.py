"""
SSO provider management service.

This module provides business logic for SSO provider configuration,
authentication session management, and metadata handling.
"""

import secrets
import xml.etree.ElementTree as ET
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List
from uuid import UUID

from sqlalchemy.orm import Session
from sqlalchemy import and_, or_

from api.models.sso import (
    SSOProvider,
    SSOAuthSession,
    SSOAttributeMapping,
    SSOProviderMetadata,
    SSOProviderType,
    SSOProviderStatus
)
from api.models.organization import Organization
from api.schemas.sso import (
    SSOProviderCreate,
    SSOProviderUpdate,
    SSOTestRequest,
    SSOTestResult
)
from api.utils.logging_config import get_logger

logger = get_logger(__name__)


class SSOService:
    """Service class for SSO provider management operations."""
    
    def __init__(self, db: Session):
        self.db = db
    
    async def create_sso_provider(
        self,
        organization_id: UUID,
        provider_data: SSOProviderCreate,
        created_by: UUID
    ) -> SSOProvider:
        """
        Create a new SSO provider configuration.
        
        Args:
            organization_id: Organization ID
            provider_data: SSO provider creation data
            created_by: User ID creating the provider
            
        Returns:
            Created SSO provider instance
            
        Raises:
            ValueError: If provider data is invalid or name already exists
        """
        # Check if organization exists
        organization = self.db.query(Organization).filter(
            and_(
                Organization.id == organization_id,
                Organization.deleted_at.is_(None)
            )
        ).first()
        
        if not organization:
            raise ValueError("Organization not found")
        
        # Check if provider name already exists for this organization
        existing_provider = self.db.query(SSOProvider).filter(
            and_(
                SSOProvider.organization_id == organization_id,
                SSOProvider.name == provider_data.name,
                SSOProvider.deleted_at.is_(None)
            )
        ).first()
        
        if existing_provider:
            raise ValueError(f"SSO provider with name '{provider_data.name}' already exists")
        
        # Create SSO provider
        sso_provider = SSOProvider(
            organization_id=organization_id,
            name=provider_data.name,
            display_name=provider_data.display_name,
            description=provider_data.description,
            provider_type=provider_data.provider_type,
            configuration=provider_data.configuration,
            attribute_mapping=provider_data.attribute_mapping.dict(),
            auto_provision_users=provider_data.auto_provision_users,
            require_encrypted_assertions=provider_data.require_encrypted_assertions,
            status=SSOProviderStatus.DRAFT
        )
        
        self.db.add(sso_provider)
        self.db.commit()
        self.db.refresh(sso_provider)
        
        logger.info(f"Created SSO provider {sso_provider.id} for organization {organization_id}")
        
        return sso_provider
    
    async def get_sso_provider(
        self,
        provider_id: UUID,
        organization_id: UUID
    ) -> Optional[SSOProvider]:
        """
        Get SSO provider by ID with organization validation.
        
        Args:
            provider_id: SSO provider ID
            organization_id: Organization ID for access validation
            
        Returns:
            SSO provider instance if found and accessible, None otherwise
        """
        return self.db.query(SSOProvider).filter(
            and_(
                SSOProvider.id == provider_id,
                SSOProvider.organization_id == organization_id,
                SSOProvider.deleted_at.is_(None)
            )
        ).first()
    
    async def update_sso_provider(
        self,
        provider_id: UUID,
        organization_id: UUID,
        provider_data: SSOProviderUpdate,
        updated_by: UUID
    ) -> Optional[SSOProvider]:
        """
        Update SSO provider configuration.
        
        Args:
            provider_id: SSO provider ID
            organization_id: Organization ID for access validation
            provider_data: Updated provider data
            updated_by: User ID performing the update
            
        Returns:
            Updated SSO provider instance if successful, None if not found
            
        Raises:
            ValueError: If update data is invalid
        """
        provider = await self.get_sso_provider(provider_id, organization_id)
        
        if not provider:
            return None
        
        # Update fields if provided
        update_data = provider_data.dict(exclude_unset=True)
        
        for field, value in update_data.items():
            if field == "attribute_mapping" and value:
                provider.attribute_mapping = value.dict()
            else:
                setattr(provider, field, value)
        
        provider.updated_at = datetime.utcnow()
        
        self.db.commit()
        self.db.refresh(provider)
        
        logger.info(f"Updated SSO provider {provider_id} by user {updated_by}")
        
        return provider
    
    async def delete_sso_provider(
        self,
        provider_id: UUID,
        organization_id: UUID,
        deleted_by: UUID
    ) -> bool:
        """
        Soft delete an SSO provider.
        
        Args:
            provider_id: SSO provider ID
            organization_id: Organization ID for access validation
            deleted_by: User ID performing the deletion
            
        Returns:
            True if successful, False if provider not found
        """
        provider = await self.get_sso_provider(provider_id, organization_id)
        
        if not provider:
            return False
        
        # Soft delete the provider
        provider.deleted_at = datetime.utcnow()
        provider.status = SSOProviderStatus.INACTIVE
        
        self.db.commit()
        
        logger.info(f"Deleted SSO provider {provider_id} by user {deleted_by}")
        
        return True
    
    async def test_sso_provider(
        self,
        provider_id: UUID,
        organization_id: UUID,
        test_request: SSOTestRequest
    ) -> SSOTestResult:
        """
        Test SSO provider configuration.
        
        Args:
            provider_id: SSO provider ID
            organization_id: Organization ID for access validation
            test_request: Test configuration
            
        Returns:
            Test result with success status and details
            
        Raises:
            ValueError: If provider not found or test type invalid
        """
        provider = await self.get_sso_provider(provider_id, organization_id)
        
        if not provider:
            raise ValueError("SSO provider not found")
        
        start_time = datetime.utcnow()
        
        try:
            if test_request.test_type == "connection":
                result = await self._test_connection(provider)
            elif test_request.test_type == "authentication":
                result = await self._test_authentication(provider, test_request)
            elif test_request.test_type == "metadata":
                result = await self._test_metadata(provider)
            else:
                raise ValueError(f"Invalid test type: {test_request.test_type}")
            
            duration_ms = int((datetime.utcnow() - start_time).total_seconds() * 1000)
            
            # Update provider test results
            provider.last_test_date = datetime.utcnow()
            provider.last_test_result = {
                "success": result["success"],
                "test_type": test_request.test_type,
                "message": result["message"],
                "tested_at": provider.last_test_date.isoformat(),
                "duration_ms": duration_ms
            }
            
            self.db.commit()
            
            return SSOTestResult(
                success=result["success"],
                test_type=test_request.test_type,
                message=result["message"],
                details=result.get("details"),
                tested_at=provider.last_test_date,
                duration_ms=duration_ms
            )
            
        except Exception as e:
            duration_ms = int((datetime.utcnow() - start_time).total_seconds() * 1000)
            
            logger.error(f"SSO provider test failed for {provider_id}: {e}")
            
            return SSOTestResult(
                success=False,
                test_type=test_request.test_type,
                message=f"Test failed: {str(e)}",
                details={"error": str(e)},
                tested_at=datetime.utcnow(),
                duration_ms=duration_ms
            )
    
    async def _test_connection(self, provider: SSOProvider) -> Dict[str, Any]:
        """Test basic connection to SSO provider."""
        if provider.provider_type == SSOProviderType.SAML2:
            return await self._test_saml2_connection(provider)
        elif provider.provider_type == SSOProviderType.OIDC:
            return await self._test_oidc_connection(provider)
        elif provider.provider_type == SSOProviderType.OAUTH2:
            return await self._test_oauth2_connection(provider)
        elif provider.provider_type == SSOProviderType.LDAP:
            return await self._test_ldap_connection(provider)
        elif provider.provider_type == SSOProviderType.ACTIVE_DIRECTORY:
            return await self._test_ad_connection(provider)
        else:
            return {"success": False, "message": "Unsupported provider type"}
    
    async def _test_saml2_connection(self, provider: SSOProvider) -> Dict[str, Any]:
        """Test SAML2 provider connection."""
        # TODO: Implement SAML2 connection test
        return {
            "success": True,
            "message": "SAML2 connection test successful",
            "details": {"provider_type": "saml2"}
        }
    
    async def _test_oidc_connection(self, provider: SSOProvider) -> Dict[str, Any]:
        """Test OIDC provider connection."""
        # TODO: Implement OIDC connection test
        return {
            "success": True,
            "message": "OIDC connection test successful",
            "details": {"provider_type": "oidc"}
        }
    
    async def _test_oauth2_connection(self, provider: SSOProvider) -> Dict[str, Any]:
        """Test OAuth2 provider connection."""
        # TODO: Implement OAuth2 connection test
        return {
            "success": True,
            "message": "OAuth2 connection test successful",
            "details": {"provider_type": "oauth2"}
        }
    
    async def _test_ldap_connection(self, provider: SSOProvider) -> Dict[str, Any]:
        """Test LDAP provider connection."""
        # TODO: Implement LDAP connection test
        return {
            "success": True,
            "message": "LDAP connection test successful",
            "details": {"provider_type": "ldap"}
        }
    
    async def _test_ad_connection(self, provider: SSOProvider) -> Dict[str, Any]:
        """Test Active Directory provider connection."""
        # TODO: Implement AD connection test
        return {
            "success": True,
            "message": "Active Directory connection test successful",
            "details": {"provider_type": "active_directory"}
        }
    
    async def _test_authentication(self, provider: SSOProvider, test_request: SSOTestRequest) -> Dict[str, Any]:
        """Test authentication with SSO provider."""
        # TODO: Implement authentication test
        return {
            "success": True,
            "message": "Authentication test successful",
            "details": {"test_user": test_request.test_user}
        }
    
    async def _test_metadata(self, provider: SSOProvider) -> Dict[str, Any]:
        """Test metadata retrieval from SSO provider."""
        # TODO: Implement metadata test
        return {
            "success": True,
            "message": "Metadata test successful",
            "details": {"metadata_url": provider.metadata_url}
        }
