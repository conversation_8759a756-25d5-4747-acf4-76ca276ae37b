"""
Enterprise integration management service.

This module provides business logic for enterprise security tool integrations,
sync job management, and data mapping operations.
"""

import asyncio
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List
from uuid import UUID, uuid4

from sqlalchemy.orm import Session
from sqlalchemy import and_, func

from api.models.integrations import (
    EnterpriseIntegration,
    IntegrationSyncJob,
    IntegrationDataMapping,
    IntegrationEvent,
    IntegrationType,
    IntegrationStatus,
    SyncStatus
)
from api.models.organization import Organization
from api.schemas.integrations import (
    EnterpriseIntegrationCreate,
    EnterpriseIntegrationUpdate,
    SyncJobCreate,
    IntegrationTestRequest,
    IntegrationTestResult
)
from api.utils.logging_config import get_logger

logger = get_logger(__name__)


class IntegrationService:
    """Service class for enterprise integration management operations."""
    
    def __init__(self, db: Session):
        self.db = db
    
    async def create_integration(
        self,
        organization_id: UUID,
        integration_data: EnterpriseIntegrationCreate,
        created_by: UUID
    ) -> EnterpriseIntegration:
        """
        Create a new enterprise integration.
        
        Args:
            organization_id: Organization ID
            integration_data: Integration creation data
            created_by: User ID creating the integration
            
        Returns:
            Created integration instance
            
        Raises:
            ValueError: If integration data is invalid or name already exists
        """
        # Check if organization exists
        organization = self.db.query(Organization).filter(
            and_(
                Organization.id == organization_id,
                Organization.deleted_at.is_(None)
            )
        ).first()
        
        if not organization:
            raise ValueError("Organization not found")
        
        # Check if integration name already exists for this organization
        existing_integration = self.db.query(EnterpriseIntegration).filter(
            and_(
                EnterpriseIntegration.organization_id == organization_id,
                EnterpriseIntegration.name == integration_data.name,
                EnterpriseIntegration.deleted_at.is_(None)
            )
        ).first()
        
        if existing_integration:
            raise ValueError(f"Integration with name '{integration_data.name}' already exists")
        
        # Create enterprise integration
        integration = EnterpriseIntegration(
            organization_id=organization_id,
            name=integration_data.name,
            display_name=integration_data.display_name,
            description=integration_data.description,
            integration_type=integration_data.integration_type,
            vendor=integration_data.vendor,
            product=integration_data.product,
            version=integration_data.version,
            connection_config=integration_data.connection_config,
            auth_config=integration_data.auth_config,
            data_mapping=[mapping.dict() for mapping in integration_data.data_mapping],
            sync_config=integration_data.sync_config.dict(),
            auto_sync=integration_data.sync_config.auto_sync,
            sync_interval_minutes=integration_data.sync_config.sync_interval_minutes,
            status=IntegrationStatus.DRAFT
        )
        
        self.db.add(integration)
        self.db.commit()
        self.db.refresh(integration)
        
        # Create data mapping records
        for mapping_rule in integration_data.data_mapping:
            data_mapping = IntegrationDataMapping(
                integration_id=integration.id,
                source_field=mapping_rule.source_field,
                target_field=mapping_rule.target_field,
                field_type=mapping_rule.field_type,
                is_required=mapping_rule.is_required,
                default_value=mapping_rule.default_value,
                transformation_rules=mapping_rule.transformation_rules,
                validation_rules=mapping_rule.validation_rules,
                direction=mapping_rule.direction
            )
            self.db.add(data_mapping)
        
        self.db.commit()
        
        logger.info(f"Created enterprise integration {integration.id} for organization {organization_id}")
        
        return integration
    
    async def get_integration(
        self,
        integration_id: UUID,
        organization_id: UUID
    ) -> Optional[EnterpriseIntegration]:
        """
        Get integration by ID with organization validation.
        
        Args:
            integration_id: Integration ID
            organization_id: Organization ID for access validation
            
        Returns:
            Integration instance if found and accessible, None otherwise
        """
        return self.db.query(EnterpriseIntegration).filter(
            and_(
                EnterpriseIntegration.id == integration_id,
                EnterpriseIntegration.organization_id == organization_id,
                EnterpriseIntegration.deleted_at.is_(None)
            )
        ).first()
    
    async def update_integration(
        self,
        integration_id: UUID,
        organization_id: UUID,
        integration_data: EnterpriseIntegrationUpdate,
        updated_by: UUID
    ) -> Optional[EnterpriseIntegration]:
        """
        Update integration configuration.
        
        Args:
            integration_id: Integration ID
            organization_id: Organization ID for access validation
            integration_data: Updated integration data
            updated_by: User ID performing the update
            
        Returns:
            Updated integration instance if successful, None if not found
            
        Raises:
            ValueError: If update data is invalid
        """
        integration = await self.get_integration(integration_id, organization_id)
        
        if not integration:
            return None
        
        # Update fields if provided
        update_data = integration_data.dict(exclude_unset=True)
        
        for field, value in update_data.items():
            if field == "data_mapping" and value:
                integration.data_mapping = [mapping.dict() for mapping in value]
            elif field == "sync_config" and value:
                integration.sync_config = value.dict()
                integration.auto_sync = value.auto_sync
                integration.sync_interval_minutes = value.sync_interval_minutes
            else:
                setattr(integration, field, value)
        
        integration.updated_at = datetime.utcnow()
        
        self.db.commit()
        self.db.refresh(integration)
        
        logger.info(f"Updated integration {integration_id} by user {updated_by}")
        
        return integration
    
    async def delete_integration(
        self,
        integration_id: UUID,
        organization_id: UUID,
        deleted_by: UUID
    ) -> bool:
        """
        Soft delete an integration.
        
        Args:
            integration_id: Integration ID
            organization_id: Organization ID for access validation
            deleted_by: User ID performing the deletion
            
        Returns:
            True if successful, False if integration not found
        """
        integration = await self.get_integration(integration_id, organization_id)
        
        if not integration:
            return False
        
        # Soft delete the integration
        integration.deleted_at = datetime.utcnow()
        integration.status = IntegrationStatus.INACTIVE
        integration.is_enabled = False
        
        self.db.commit()
        
        logger.info(f"Deleted integration {integration_id} by user {deleted_by}")
        
        return True
    
    async def test_integration(
        self,
        integration_id: UUID,
        organization_id: UUID,
        test_request: IntegrationTestRequest
    ) -> IntegrationTestResult:
        """
        Test integration configuration.
        
        Args:
            integration_id: Integration ID
            organization_id: Organization ID for access validation
            test_request: Test configuration
            
        Returns:
            Test result with success status and details
            
        Raises:
            ValueError: If integration not found or test type invalid
        """
        integration = await self.get_integration(integration_id, organization_id)
        
        if not integration:
            raise ValueError("Integration not found")
        
        start_time = datetime.utcnow()
        
        try:
            if test_request.test_type == "connection":
                result = await self._test_connection(integration)
            elif test_request.test_type == "authentication":
                result = await self._test_authentication(integration)
            elif test_request.test_type == "data_mapping":
                result = await self._test_data_mapping(integration)
            elif test_request.test_type == "sync":
                result = await self._test_sync(integration)
            else:
                raise ValueError(f"Invalid test type: {test_request.test_type}")
            
            duration_ms = int((datetime.utcnow() - start_time).total_seconds() * 1000)
            
            # Update integration test results
            integration.last_test_date = datetime.utcnow()
            integration.last_test_result = {
                "success": result["success"],
                "test_type": test_request.test_type,
                "message": result["message"],
                "tested_at": integration.last_test_date.isoformat(),
                "duration_ms": duration_ms
            }
            
            self.db.commit()
            
            return IntegrationTestResult(
                success=result["success"],
                test_type=test_request.test_type,
                message=result["message"],
                details=result.get("details"),
                tested_at=integration.last_test_date,
                duration_ms=duration_ms
            )
            
        except Exception as e:
            duration_ms = int((datetime.utcnow() - start_time).total_seconds() * 1000)
            
            logger.error(f"Integration test failed for {integration_id}: {e}")
            
            return IntegrationTestResult(
                success=False,
                test_type=test_request.test_type,
                message=f"Test failed: {str(e)}",
                details={"error": str(e)},
                tested_at=datetime.utcnow(),
                duration_ms=duration_ms
            )
    
    async def _test_connection(self, integration: EnterpriseIntegration) -> Dict[str, Any]:
        """Test connection to integration endpoint."""
        # TODO: Implement connection testing based on integration type
        return {
            "success": True,
            "message": f"Connection test successful for {integration.integration_type}",
            "details": {"integration_type": integration.integration_type}
        }
    
    async def _test_authentication(self, integration: EnterpriseIntegration) -> Dict[str, Any]:
        """Test authentication with integration."""
        # TODO: Implement authentication testing
        return {
            "success": True,
            "message": "Authentication test successful",
            "details": {"auth_type": "configured"}
        }
    
    async def _test_data_mapping(self, integration: EnterpriseIntegration) -> Dict[str, Any]:
        """Test data mapping configuration."""
        # TODO: Implement data mapping testing
        return {
            "success": True,
            "message": "Data mapping test successful",
            "details": {"mappings_count": len(integration.data_mapping)}
        }
    
    async def _test_sync(self, integration: EnterpriseIntegration) -> Dict[str, Any]:
        """Test sync operation."""
        # TODO: Implement sync testing
        return {
            "success": True,
            "message": "Sync test successful",
            "details": {"sync_type": "test"}
        }
