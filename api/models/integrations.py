"""
Enterprise security tool integration models.

This module defines the database models for integrating with enterprise
security tools including SIEM, SOAR, TIP, vulnerability scanners, and more.
"""

from datetime import datetime
from typing import Dict, Any, List, Optional
from uuid import uuid4
from enum import Enum

from sqlalchemy import (
    Column, Integer, String, Text, Boolean, DateTime, JSON, 
    ForeignKey, Enum as SQLEnum, UniqueConstraint, Index
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from api.database import Base
from api.models.mixins import TimestampMixin, SoftDeleteMixin


class IntegrationType(str, Enum):
    """Integration type enumeration."""
    SIEM = "siem"
    SOAR = "soar"
    TIP = "tip"  # Threat Intelligence Platform
    VULNERABILITY_SCANNER = "vulnerability_scanner"
    EDR = "edr"  # Endpoint Detection and Response
    NETWORK_SCANNER = "network_scanner"
    COMPLIANCE_TOOL = "compliance_tool"
    TICKETING_SYSTEM = "ticketing_system"
    NOTIFICATION_SYSTEM = "notification_system"


class IntegrationStatus(str, Enum):
    """Integration status enumeration."""
    DRAFT = "draft"
    ACTIVE = "active"
    INACTIVE = "inactive"
    ERROR = "error"
    TESTING = "testing"
    SYNCING = "syncing"


class SyncStatus(str, Enum):
    """Sync status enumeration."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class EnterpriseIntegration(Base, TimestampMixin, SoftDeleteMixin):
    """
    Enterprise security tool integration model.
    
    Stores configuration for integrating with various enterprise
    security tools and platforms.
    """
    __tablename__ = "enterprise_integrations"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4, index=True)
    organization_id = Column(UUID(as_uuid=True), ForeignKey("organizations.id"), nullable=False)
    
    # Integration basic information
    name = Column(String(255), nullable=False)
    display_name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    integration_type = Column(
        SQLEnum(IntegrationType),
        nullable=False
    )
    status = Column(
        SQLEnum(IntegrationStatus),
        nullable=False,
        default=IntegrationStatus.DRAFT
    )
    
    # Vendor and product information
    vendor = Column(String(255), nullable=False)
    product = Column(String(255), nullable=False)
    version = Column(String(100), nullable=True)
    
    # Connection configuration
    connection_config = Column(JSON, nullable=False, default=dict)
    
    # Authentication configuration
    auth_config = Column(JSON, nullable=False, default=dict)
    
    # Data mapping configuration
    data_mapping = Column(JSON, nullable=False, default=dict)
    
    # Sync configuration
    sync_config = Column(JSON, nullable=False, default=dict)
    
    # Settings
    is_enabled = Column(Boolean, default=True, nullable=False)
    auto_sync = Column(Boolean, default=False, nullable=False)
    sync_interval_minutes = Column(Integer, default=60, nullable=False)
    
    # Testing and validation
    last_test_date = Column(DateTime, nullable=True)
    last_test_result = Column(JSON, nullable=True)
    last_sync_date = Column(DateTime, nullable=True)
    last_sync_result = Column(JSON, nullable=True)
    
    # Relationships
    organization = relationship("Organization", foreign_keys=[organization_id])
    sync_jobs = relationship("IntegrationSyncJob", back_populates="integration")
    data_mappings = relationship("IntegrationDataMapping", back_populates="integration")
    
    __table_args__ = (
        UniqueConstraint('organization_id', 'name', name='uq_enterprise_integrations_org_name'),
        Index('idx_enterprise_integrations_organization', 'organization_id'),
        Index('idx_enterprise_integrations_type', 'integration_type'),
        Index('idx_enterprise_integrations_status', 'status'),
        Index('idx_enterprise_integrations_vendor', 'vendor'),
        {"extend_existing": True}
    )
    
    def __repr__(self):
        return f"<EnterpriseIntegration(name='{self.name}', type='{self.integration_type}', vendor='{self.vendor}')>"


class IntegrationSyncJob(Base, TimestampMixin):
    """
    Integration synchronization job tracking.
    
    Tracks data synchronization jobs between the platform
    and enterprise security tools.
    """
    __tablename__ = "integration_sync_jobs"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4, index=True)
    integration_id = Column(UUID(as_uuid=True), ForeignKey("enterprise_integrations.id"), nullable=False)
    
    # Job information
    job_type = Column(
        SQLEnum(
            "full_sync", "incremental_sync", "export", "import", "test",
            name="sync_job_type_enum"
        ),
        nullable=False
    )
    status = Column(
        SQLEnum(SyncStatus),
        nullable=False,
        default=SyncStatus.PENDING
    )
    
    # Timing
    started_at = Column(DateTime, nullable=True)
    completed_at = Column(DateTime, nullable=True)
    scheduled_at = Column(DateTime, nullable=False, default=func.now())
    
    # Progress tracking
    total_records = Column(Integer, nullable=True)
    processed_records = Column(Integer, default=0, nullable=False)
    success_records = Column(Integer, default=0, nullable=False)
    error_records = Column(Integer, default=0, nullable=False)
    
    # Results and errors
    result_summary = Column(JSON, nullable=True)
    error_details = Column(JSON, nullable=True)
    
    # Configuration
    sync_parameters = Column(JSON, nullable=True)
    
    # Relationships
    integration = relationship("EnterpriseIntegration", back_populates="sync_jobs")
    
    __table_args__ = (
        Index('idx_integration_sync_jobs_integration', 'integration_id'),
        Index('idx_integration_sync_jobs_status', 'status'),
        Index('idx_integration_sync_jobs_scheduled', 'scheduled_at'),
        Index('idx_integration_sync_jobs_type', 'job_type'),
        {"extend_existing": True}
    )
    
    def __repr__(self):
        return f"<IntegrationSyncJob(id={self.id}, type='{self.job_type}', status='{self.status}')>"


class IntegrationDataMapping(Base, TimestampMixin, SoftDeleteMixin):
    """
    Integration data mapping configuration.
    
    Defines how data fields map between the platform and
    external enterprise security tools.
    """
    __tablename__ = "integration_data_mappings"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4, index=True)
    integration_id = Column(UUID(as_uuid=True), ForeignKey("enterprise_integrations.id"), nullable=False)
    
    # Mapping configuration
    source_field = Column(String(255), nullable=False)
    target_field = Column(String(255), nullable=False)
    field_type = Column(String(50), nullable=False)  # string, integer, boolean, date, json
    is_required = Column(Boolean, default=False, nullable=False)
    default_value = Column(Text, nullable=True)
    
    # Transformation rules
    transformation_rules = Column(JSON, nullable=True)
    validation_rules = Column(JSON, nullable=True)
    
    # Direction
    direction = Column(
        SQLEnum(
            "inbound", "outbound", "bidirectional",
            name="mapping_direction_enum"
        ),
        nullable=False,
        default="bidirectional"
    )
    
    # Relationships
    integration = relationship("EnterpriseIntegration", back_populates="data_mappings")
    
    __table_args__ = (
        UniqueConstraint('integration_id', 'source_field', 'target_field', name='uq_integration_data_mapping'),
        Index('idx_integration_data_mappings_integration', 'integration_id'),
        Index('idx_integration_data_mappings_source', 'source_field'),
        Index('idx_integration_data_mappings_target', 'target_field'),
        {"extend_existing": True}
    )
    
    def __repr__(self):
        return f"<IntegrationDataMapping(source='{self.source_field}', target='{self.target_field}')>"


class IntegrationEvent(Base, TimestampMixin):
    """
    Integration event logging.
    
    Logs events and activities related to enterprise integrations
    for audit and troubleshooting purposes.
    """
    __tablename__ = "integration_events"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4, index=True)
    integration_id = Column(UUID(as_uuid=True), ForeignKey("enterprise_integrations.id"), nullable=False)
    sync_job_id = Column(UUID(as_uuid=True), ForeignKey("integration_sync_jobs.id"), nullable=True)
    
    # Event information
    event_type = Column(
        SQLEnum(
            "connection_test", "sync_started", "sync_completed", "sync_failed",
            "data_received", "data_sent", "error", "warning", "info",
            name="integration_event_type_enum"
        ),
        nullable=False
    )
    severity = Column(
        SQLEnum(
            "low", "medium", "high", "critical",
            name="event_severity_enum"
        ),
        nullable=False,
        default="info"
    )
    
    # Event details
    message = Column(Text, nullable=False)
    details = Column(JSON, nullable=True)
    
    # Context
    user_id = Column(UUID(as_uuid=True), nullable=True)
    source_ip = Column(String(45), nullable=True)
    
    # Relationships
    integration = relationship("EnterpriseIntegration", foreign_keys=[integration_id])
    sync_job = relationship("IntegrationSyncJob", foreign_keys=[sync_job_id])
    
    __table_args__ = (
        Index('idx_integration_events_integration', 'integration_id'),
        Index('idx_integration_events_sync_job', 'sync_job_id'),
        Index('idx_integration_events_type', 'event_type'),
        Index('idx_integration_events_severity', 'severity'),
        Index('idx_integration_events_created', 'created_at'),
        {"extend_existing": True}
    )
    
    def __repr__(self):
        return f"<IntegrationEvent(type='{self.event_type}', severity='{self.severity}')>"
