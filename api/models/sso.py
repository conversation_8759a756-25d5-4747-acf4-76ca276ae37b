"""
SSO (Single Sign-On) provider models for enterprise authentication.

This module defines the database models for SSO provider configuration,
including SAML2, OIDC, OAuth2, LDAP, and Active Directory integrations.
"""

from datetime import datetime
from typing import Dict, Any, List, Optional
from uuid import uuid4
from enum import Enum

from sqlalchemy import (
    Column, Integer, String, Text, Boolean, DateTime, JSON, 
    ForeignKey, Enum as SQLEnum, UniqueConstraint, Index
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from api.database import Base
from api.models.mixins import TimestampMixin, SoftDeleteMixin


class SSOProviderType(str, Enum):
    """SSO provider type enumeration."""
    SAML2 = "saml2"
    OIDC = "oidc"
    OAUTH2 = "oauth2"
    LDAP = "ldap"
    ACTIVE_DIRECTORY = "active_directory"


class SSOProviderStatus(str, Enum):
    """SSO provider status enumeration."""
    DRAFT = "draft"
    ACTIVE = "active"
    INACTIVE = "inactive"
    ERROR = "error"
    TESTING = "testing"


class SSOProvider(Base, TimestampMixin, SoftDeleteMixin):
    """
    SSO Provider configuration model.
    
    Stores configuration for various SSO providers including SAML2, OIDC,
    OAuth2, LDAP, and Active Directory integrations.
    """
    __tablename__ = "sso_providers"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4, index=True)
    organization_id = Column(UUID(as_uuid=True), ForeignKey("organizations.id"), nullable=False)
    
    # Provider basic information
    name = Column(String(255), nullable=False)
    display_name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    provider_type = Column(
        SQLEnum(SSOProviderType),
        nullable=False
    )
    status = Column(
        SQLEnum(SSOProviderStatus),
        nullable=False,
        default=SSOProviderStatus.DRAFT
    )
    
    # Provider configuration (stored as JSON)
    configuration = Column(JSON, nullable=False, default=dict)
    
    # Metadata and endpoints
    metadata_url = Column(String(500), nullable=True)
    entity_id = Column(String(500), nullable=True)
    sso_url = Column(String(500), nullable=True)
    slo_url = Column(String(500), nullable=True)  # Single Logout URL
    
    # Certificates and security
    certificate = Column(Text, nullable=True)
    private_key = Column(Text, nullable=True)
    
    # Attribute mapping
    attribute_mapping = Column(JSON, nullable=False, default=dict)
    
    # Settings
    is_default = Column(Boolean, default=False, nullable=False)
    auto_provision_users = Column(Boolean, default=False, nullable=False)
    require_encrypted_assertions = Column(Boolean, default=True, nullable=False)
    
    # Testing and validation
    last_test_date = Column(DateTime, nullable=True)
    last_test_result = Column(JSON, nullable=True)
    
    # Relationships
    organization = relationship("Organization", foreign_keys=[organization_id])
    auth_sessions = relationship("SSOAuthSession", back_populates="provider")
    
    __table_args__ = (
        UniqueConstraint('organization_id', 'name', name='uq_sso_providers_org_name'),
        Index('idx_sso_providers_organization', 'organization_id'),
        Index('idx_sso_providers_type', 'provider_type'),
        Index('idx_sso_providers_status', 'status'),
        Index('idx_sso_providers_default', 'is_default'),
        {"extend_existing": True}
    )
    
    def __repr__(self):
        return f"<SSOProvider(name='{self.name}', type='{self.provider_type}', status='{self.status}')>"


class SSOAuthSession(Base, TimestampMixin):
    """
    SSO authentication session tracking.
    
    Tracks SSO authentication sessions for audit and debugging purposes.
    """
    __tablename__ = "sso_auth_sessions"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4, index=True)
    provider_id = Column(UUID(as_uuid=True), ForeignKey("sso_providers.id"), nullable=False)
    user_id = Column(UUID(as_uuid=True), nullable=True)  # Will be linked to User model
    
    # Session information
    session_id = Column(String(255), nullable=False, unique=True, index=True)
    request_id = Column(String(255), nullable=True)
    
    # Authentication details
    initiated_at = Column(DateTime, nullable=False, default=func.now())
    completed_at = Column(DateTime, nullable=True)
    status = Column(
        SQLEnum(
            "initiated", "in_progress", "completed", "failed", "expired",
            name="sso_auth_status_enum"
        ),
        nullable=False,
        default="initiated"
    )
    
    # User information from SSO
    sso_user_id = Column(String(255), nullable=True)
    sso_attributes = Column(JSON, nullable=True)
    
    # Error information
    error_code = Column(String(50), nullable=True)
    error_message = Column(Text, nullable=True)
    
    # Client information
    client_ip = Column(String(45), nullable=True)
    user_agent = Column(Text, nullable=True)
    
    # Relationships
    provider = relationship("SSOProvider", back_populates="auth_sessions")
    
    __table_args__ = (
        Index('idx_sso_auth_sessions_provider', 'provider_id'),
        Index('idx_sso_auth_sessions_user', 'user_id'),
        Index('idx_sso_auth_sessions_status', 'status'),
        Index('idx_sso_auth_sessions_initiated', 'initiated_at'),
        {"extend_existing": True}
    )
    
    def __repr__(self):
        return f"<SSOAuthSession(session_id='{self.session_id}', status='{self.status}')>"


class SSOAttributeMapping(Base, TimestampMixin, SoftDeleteMixin):
    """
    SSO attribute mapping configuration.
    
    Defines how SSO provider attributes map to internal user attributes.
    """
    __tablename__ = "sso_attribute_mappings"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4, index=True)
    provider_id = Column(UUID(as_uuid=True), ForeignKey("sso_providers.id"), nullable=False)
    
    # Mapping configuration
    sso_attribute = Column(String(255), nullable=False)
    internal_attribute = Column(String(255), nullable=False)
    is_required = Column(Boolean, default=False, nullable=False)
    default_value = Column(String(500), nullable=True)
    
    # Transformation rules
    transformation_rules = Column(JSON, nullable=True)
    
    # Relationships
    provider = relationship("SSOProvider", foreign_keys=[provider_id])
    
    __table_args__ = (
        UniqueConstraint('provider_id', 'sso_attribute', name='uq_sso_attr_mapping_provider_attr'),
        Index('idx_sso_attr_mappings_provider', 'provider_id'),
        Index('idx_sso_attr_mappings_internal', 'internal_attribute'),
        {"extend_existing": True}
    )
    
    def __repr__(self):
        return f"<SSOAttributeMapping(sso='{self.sso_attribute}', internal='{self.internal_attribute}')>"


class SSOProviderMetadata(Base, TimestampMixin):
    """
    SSO provider metadata cache.
    
    Caches metadata from SSO providers for performance and offline access.
    """
    __tablename__ = "sso_provider_metadata"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4, index=True)
    provider_id = Column(UUID(as_uuid=True), ForeignKey("sso_providers.id"), nullable=False)
    
    # Metadata information
    metadata_xml = Column(Text, nullable=True)
    metadata_json = Column(JSON, nullable=True)
    metadata_url = Column(String(500), nullable=True)
    
    # Cache information
    fetched_at = Column(DateTime, nullable=False, default=func.now())
    expires_at = Column(DateTime, nullable=True)
    is_valid = Column(Boolean, default=True, nullable=False)
    
    # Validation results
    validation_errors = Column(JSON, nullable=True)
    
    # Relationships
    provider = relationship("SSOProvider", foreign_keys=[provider_id])
    
    __table_args__ = (
        UniqueConstraint('provider_id', name='uq_sso_metadata_provider'),
        Index('idx_sso_metadata_provider', 'provider_id'),
        Index('idx_sso_metadata_expires', 'expires_at'),
        Index('idx_sso_metadata_valid', 'is_valid'),
        {"extend_existing": True}
    )
    
    def __repr__(self):
        return f"<SSOProviderMetadata(provider_id={self.provider_id}, valid={self.is_valid})>"
