"""
NIST Cybersecurity Framework (CSF) 2.0 models.

This module implements SQLAlchemy models for the NIST Cybersecurity Framework 2.0,
including versions, functions, categories, subcategories, implementation examples,
and informative references.

The models support the complete CSF 2.0 hierarchy:
- Functions (Govern, Identify, Protect, Detect, Respond, Recover)
- Categories within each function
- Subcategories with detailed implementation guidance
- Implementation examples and informative references
"""

from datetime import datetime
from typing import List, Optional, Dict, Any

from sqlalchemy import (
    Boolean,
    Column,
    DateTime,
    ForeignKey,
    Integer,
    String,
    Text,
    UniqueConstraint,
    Index
)
from sqlalchemy.orm import relationship, Session, Mapped, mapped_column
from sqlalchemy.sql import func

from api.database import Base
from api.models.base import VersionMixin


class NISTCSFVersion(Base, VersionMixin):
    """
    NIST Cybersecurity Framework version management.
    
    Tracks different versions of the NIST CSF (1.1, 2.0, future versions)
    with proper version control and supersession relationships.
    """
    
    __tablename__ = "nist_csf_versions"
    __table_args__ = (
        Index('idx_nist_csf_versions_current', 'is_current'),
        Index('idx_nist_csf_versions_not_deleted', 'deleted_time'),
        {"extend_existing": True}
    )
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    version: Mapped[str] = mapped_column(String(50), nullable=False, unique=True)
    release_date: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    import_date: Mapped[datetime] = mapped_column(DateTime, nullable=False, default=datetime.utcnow)
    is_current: Mapped[bool] = mapped_column(Boolean, nullable=False, default=False)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    url: Mapped[Optional[str]] = mapped_column(String(500), nullable=True)
    deprecated: Mapped[bool] = mapped_column(Boolean, nullable=False, default=False)
    deprecation_date: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    supersedes_version_id: Mapped[Optional[int]] = mapped_column(Integer, ForeignKey("nist_csf_versions.id", ondelete="SET NULL"), nullable=True)
    
    # Relationships
    supersedes_version: Mapped[Optional["NISTCSFVersion"]] = relationship(
        "NISTCSFVersion", 
        remote_side=[id],
        back_populates="superseded_by_version"
    )
    superseded_by_version: Mapped[Optional["NISTCSFVersion"]] = relationship(
        "NISTCSFVersion",
        back_populates="supersedes_version"
    )
    
    functions: Mapped[List["NISTCSFFunction"]] = relationship(
        "NISTCSFFunction", 
        back_populates="version",
        cascade="all, delete-orphan",
        order_by="NISTCSFFunction.order_index"
    )
    categories: Mapped[List["NISTCSFCategory"]] = relationship(
        "NISTCSFCategory",
        back_populates="version", 
        cascade="all, delete-orphan",
        order_by="NISTCSFCategory.order_index"
    )
    subcategories: Mapped[List["NISTCSFSubcategory"]] = relationship(
        "NISTCSFSubcategory",
        back_populates="version", 
        cascade="all, delete-orphan",
        order_by="NISTCSFSubcategory.order_index"
    )
    

    
    def __repr__(self) -> str:
        return f"<NISTCSFVersion(version='{self.version}', is_current={self.is_current})>"
    
    @classmethod
    def get_current_version(cls, db: Session) -> Optional["NISTCSFVersion"]:
        """Get the current NIST CSF version."""
        return db.query(cls).filter(
            cls.is_current == True,
            cls.not_deleted()
        ).first()
    
    def set_as_current(self, db: Session) -> None:
        """Set this version as current and unset all others."""
        # Unset all other current versions
        db.query(NISTCSFVersion).filter(
            NISTCSFVersion.is_current == True,
            NISTCSFVersion.id != self.id
        ).update({"is_current": False})
        
        # Set this version as current
        self.is_current = True
        db.commit()


class NISTCSFFunction(Base, VersionMixin):
    """
    NIST CSF Functions.
    
    Represents the six core functions in NIST CSF 2.0:
    - Govern (GV)
    - Identify (ID) 
    - Protect (PR)
    - Detect (DE)
    - Respond (RS)
    - Recover (RC)
    """
    
    __tablename__ = "nist_csf_functions"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    function_id: Mapped[str] = mapped_column(String(10), nullable=False)
    name: Mapped[str] = mapped_column(String(100), nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    version_id: Mapped[int] = mapped_column(Integer, ForeignKey("nist_csf_versions.id", ondelete="CASCADE"), nullable=False)
    order_index: Mapped[int] = mapped_column(Integer, nullable=False, default=0)
    
    # Relationships
    version: Mapped["NISTCSFVersion"] = relationship("NISTCSFVersion", back_populates="functions")
    categories: Mapped[List["NISTCSFCategory"]] = relationship(
        "NISTCSFCategory",
        back_populates="function",
        cascade="all, delete-orphan",
        order_by="NISTCSFCategory.order_index"
    )
    subcategories: Mapped[List["NISTCSFSubcategory"]] = relationship(
        "NISTCSFSubcategory",
        back_populates="function",
        cascade="all, delete-orphan",
        order_by="NISTCSFSubcategory.order_index"
    )
    
    __table_args__ = (
        UniqueConstraint('function_id', 'version_id', name='uq_nist_csf_functions_function_version'),
        Index('idx_nist_csf_functions_version', 'version_id'),
        Index('idx_nist_csf_functions_not_deleted', 'deleted_time'),
        {"extend_existing": True}
    )
    
    def __repr__(self) -> str:
        return f"<NISTCSFFunction(function_id='{self.function_id}', name='{self.name}')>"


class NISTCSFCategory(Base, VersionMixin):
    """
    NIST CSF Categories.
    
    Categories within each NIST CSF function that group related
    cybersecurity outcomes and activities.
    """
    
    __tablename__ = "nist_csf_categories"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    category_id: Mapped[str] = mapped_column(String(20), nullable=False)
    name: Mapped[str] = mapped_column(String(255), nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    function_id: Mapped[int] = mapped_column(Integer, ForeignKey("nist_csf_functions.id", ondelete="CASCADE"), nullable=False)
    version_id: Mapped[int] = mapped_column(Integer, ForeignKey("nist_csf_versions.id", ondelete="CASCADE"), nullable=False)
    order_index: Mapped[int] = mapped_column(Integer, nullable=False, default=0)
    
    # Relationships
    function: Mapped["NISTCSFFunction"] = relationship("NISTCSFFunction", back_populates="categories")
    version: Mapped["NISTCSFVersion"] = relationship("NISTCSFVersion", back_populates="categories")
    subcategories: Mapped[List["NISTCSFSubcategory"]] = relationship(
        "NISTCSFSubcategory",
        back_populates="category",
        cascade="all, delete-orphan",
        order_by="NISTCSFSubcategory.order_index"
    )
    
    __table_args__ = (
        UniqueConstraint('category_id', 'version_id', name='uq_nist_csf_categories_category_version'),
        Index('idx_nist_csf_categories_function', 'function_id'),
        Index('idx_nist_csf_categories_version', 'version_id'),
        Index('idx_nist_csf_categories_not_deleted', 'deleted_time'),
        {"extend_existing": True}
    )
    
    def __repr__(self) -> str:
        return f"<NISTCSFCategory(category_id='{self.category_id}', name='{self.name}')>"


class NISTCSFSubcategory(Base, VersionMixin):
    """
    NIST CSF Subcategories.
    
    Detailed subcategories that provide specific cybersecurity outcomes
    and implementation guidance within each category.
    """
    
    __tablename__ = "nist_csf_subcategories"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    subcategory_id: Mapped[str] = mapped_column(String(30), nullable=False)
    name: Mapped[str] = mapped_column(String(500), nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    category_id: Mapped[int] = mapped_column(Integer, ForeignKey("nist_csf_categories.id", ondelete="CASCADE"), nullable=False)
    function_id: Mapped[int] = mapped_column(Integer, ForeignKey("nist_csf_functions.id", ondelete="CASCADE"), nullable=False)
    version_id: Mapped[int] = mapped_column(Integer, ForeignKey("nist_csf_versions.id", ondelete="CASCADE"), nullable=False)
    order_index: Mapped[int] = mapped_column(Integer, nullable=False, default=0)
    
    # Relationships
    category: Mapped["NISTCSFCategory"] = relationship("NISTCSFCategory", back_populates="subcategories")
    function: Mapped["NISTCSFFunction"] = relationship("NISTCSFFunction", back_populates="subcategories")
    version: Mapped["NISTCSFVersion"] = relationship("NISTCSFVersion", back_populates="subcategories")
    implementation_examples: Mapped[List["NISTCSFImplementationExample"]] = relationship(
        "NISTCSFImplementationExample",
        back_populates="subcategory",
        cascade="all, delete-orphan",
        order_by="NISTCSFImplementationExample.order_index"
    )
    informative_references: Mapped[List["NISTCSFInformativeReference"]] = relationship(
        "NISTCSFInformativeReference",
        back_populates="subcategory",
        cascade="all, delete-orphan"
    )
    
    __table_args__ = (
        UniqueConstraint('subcategory_id', 'version_id', name='uq_nist_csf_subcategories_subcategory_version'),
        Index('idx_nist_csf_subcategories_category', 'category_id'),
        Index('idx_nist_csf_subcategories_function', 'function_id'),
        Index('idx_nist_csf_subcategories_version', 'version_id'),
        Index('idx_nist_csf_subcategories_not_deleted', 'deleted_time'),
        {"extend_existing": True}
    )
    
    def __repr__(self) -> str:
        return f"<NISTCSFSubcategory(subcategory_id='{self.subcategory_id}', name='{self.name[:50]}...')>"


class NISTCSFImplementationExample(Base, VersionMixin):
    """
    NIST CSF Implementation Examples.
    
    Practical implementation examples for NIST CSF subcategories
    that provide guidance on how to achieve the desired outcomes.
    """
    
    __tablename__ = "nist_csf_implementation_examples"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    subcategory_id: Mapped[int] = mapped_column(Integer, ForeignKey("nist_csf_subcategories.id", ondelete="CASCADE"), nullable=False)
    example_text: Mapped[str] = mapped_column(Text, nullable=False)
    example_type: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    order_index: Mapped[int] = mapped_column(Integer, nullable=False, default=0)
    
    # Relationships
    subcategory: Mapped["NISTCSFSubcategory"] = relationship("NISTCSFSubcategory", back_populates="implementation_examples")
    
    __table_args__ = (
        Index('idx_nist_csf_impl_examples_subcategory', 'subcategory_id'),
        Index('idx_nist_csf_impl_examples_not_deleted', 'deleted_time'),
        {"extend_existing": True}
    )
    
    def __repr__(self) -> str:
        return f"<NISTCSFImplementationExample(subcategory_id={self.subcategory_id}, example_type='{self.example_type}')>"


class NISTCSFInformativeReference(Base, VersionMixin):
    """
    NIST CSF Informative References.
    
    Cross-references to other frameworks and standards that relate
    to specific NIST CSF subcategories.
    """
    
    __tablename__ = "nist_csf_informative_references"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    subcategory_id: Mapped[int] = mapped_column(Integer, ForeignKey("nist_csf_subcategories.id", ondelete="CASCADE"), nullable=False)
    framework_name: Mapped[str] = mapped_column(String(100), nullable=False)
    reference_id: Mapped[str] = mapped_column(String(50), nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    url: Mapped[Optional[str]] = mapped_column(String(500), nullable=True)
    
    # Relationships
    subcategory: Mapped["NISTCSFSubcategory"] = relationship("NISTCSFSubcategory", back_populates="informative_references")
    
    __table_args__ = (
        Index('idx_nist_csf_info_refs_subcategory', 'subcategory_id'),
        Index('idx_nist_csf_info_refs_framework', 'framework_name'),
        Index('idx_nist_csf_info_refs_not_deleted', 'deleted_time'),
        {"extend_existing": True}
    )
    
    def __repr__(self) -> str:
        return f"<NISTCSFInformativeReference(framework_name='{self.framework_name}', reference_id='{self.reference_id}')>"
