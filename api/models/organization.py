"""
Organization-related models for multi-tenancy and enterprise features.

This module defines the database models for organization management,
tenant isolation, and subscription management in the enterprise platform.
"""

from datetime import datetime
from typing import Dict, Any, List, Optional
from uuid import uuid4

from sqlalchemy import (
    JSON, Column, DateTime, Integer, String, Text, Boolean,
    ForeignKey, Enum as SQLEnum, Float, UniqueConstraint
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from api.database import Base
from api.models.mixins import SoftDeleteMixin, TimestampMixin
from api.models.relationships import campaign_to_organization

class OrganizationDB(Base, SoftDeleteMixin):
    """Legacy organization model - kept for backward compatibility."""
    __tablename__ = "organizations_legacy"
    __table_args__ = {"extend_existing": True}

    id = Column(Integer, primary_key=True)
    name = Column(String(255), nullable=False)
    description = Column(String(1000))
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    campaigns = relationship("CampaignDB", secondary=campaign_to_organization, back_populates="organizations")


class Organization(Base, TimestampMixin, SoftDeleteMixin):
    """
    Enhanced organization model for multi-tenant architecture.

    Represents a tenant organization with its settings, subscription,
    and resource limits for enterprise features.
    """
    __tablename__ = "organizations"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4, index=True)
    name = Column(String(255), nullable=False, index=True)
    display_name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)

    # Subscription and billing
    subscription_tier = Column(
        SQLEnum(
            "starter", "professional", "enterprise", "custom",
            name="subscription_tier_enum"
        ),
        nullable=False,
        default="starter"
    )

    # Organization settings stored as JSON
    settings = Column(JSON, nullable=False, default=dict)

    # Contact information
    contact_info = Column(JSON, nullable=False, default=dict)

    # Compliance requirements
    compliance_requirements = Column(JSON, nullable=False, default=list)

    # Status and flags
    is_active = Column(Boolean, default=True, nullable=False)
    is_trial = Column(Boolean, default=False, nullable=False)
    trial_expires_at = Column(DateTime, nullable=True)

    # Legacy compatibility
    legacy_id = Column(Integer, nullable=True, index=True)

    def __repr__(self):
        return f"<Organization(id={self.id}, name='{self.name}')>"

class CLCampaign(Base, SoftDeleteMixin):
    """Campaign model for organising related security activities."""

    __tablename__ = "cl_campaign"
    __table_args__ = {"extend_existing": True}

    id = Column(Integer, primary_key=True)
    name = Column(String(255), nullable=False)
    description = Column(String(2000))
    start_date = Column(DateTime)
    end_date = Column(DateTime)
    status = Column(String(50))
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    meta_data = Column(JSON)  # Additional metadata

    # Relationships
    organizations = relationship("OrganizationDB", secondary=campaign_to_organization, back_populates="campaigns")


class TenantResource(Base, TimestampMixin):
    """
    Tenant resource allocation and usage tracking.

    Tracks resource allocation, usage metrics, and limits
    for each tenant organization.
    """
    __tablename__ = "tenant_resources"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4, index=True)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("organizations.id"), nullable=False)

    resource_type = Column(
        SQLEnum(
            "compute", "storage", "network", "database", "cache",
            name="resource_type_enum"
        ),
        nullable=False
    )

    resource_id = Column(UUID(as_uuid=True), nullable=False)
    allocated_at = Column(DateTime, nullable=False, default=func.now())

    # Usage metrics stored as JSON
    usage_metrics = Column(JSON, nullable=False, default=dict)

    # Resource limits
    limits = Column(JSON, nullable=False, default=dict)

    # Status
    is_active = Column(Boolean, default=True, nullable=False)

    # Relationships
    organization = relationship("Organization", foreign_keys=[tenant_id])

    __table_args__ = (
        UniqueConstraint('tenant_id', 'resource_type', 'resource_id',
                        name='uq_tenant_resource'),
    )

    def __repr__(self):
        return f"<TenantResource(tenant_id={self.tenant_id}, type={self.resource_type})>"


class OrganizationInvitation(Base, TimestampMixin):
    """
    Organization invitation model for user onboarding.

    Manages invitations sent to users to join organizations
    with specific roles and permissions.
    """
    __tablename__ = "organization_invitations"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4, index=True)
    organization_id = Column(UUID(as_uuid=True), ForeignKey("organizations.id"), nullable=False)

    # Invitation details
    email = Column(String(255), nullable=False, index=True)
    role = Column(String(50), nullable=False, default="member")
    invited_by = Column(UUID(as_uuid=True), nullable=True)  # Will be linked to User model

    # Invitation token and status
    token = Column(String(255), nullable=False, unique=True, index=True)
    status = Column(
        SQLEnum(
            "pending", "accepted", "declined", "expired",
            name="invitation_status_enum"
        ),
        nullable=False,
        default="pending"
    )

    # Expiration
    expires_at = Column(DateTime, nullable=False)
    accepted_at = Column(DateTime, nullable=True)

    # Relationships
    organization = relationship("Organization", foreign_keys=[organization_id])

    def __repr__(self):
        return f"<OrganizationInvitation(email='{self.email}', status='{self.status}')>"


class OrganizationUsage(Base, TimestampMixin):
    """
    Organization usage tracking and billing metrics.

    Tracks usage metrics for billing, quota management,
    and capacity planning purposes.
    """
    __tablename__ = "organization_usage"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4, index=True)
    organization_id = Column(UUID(as_uuid=True), ForeignKey("organizations.id"), nullable=False)

    # Usage period
    period_start = Column(DateTime, nullable=False)
    period_end = Column(DateTime, nullable=False)

    # Usage metrics
    active_users = Column(Integer, nullable=False, default=0)
    api_requests = Column(Integer, nullable=False, default=0)
    storage_used_gb = Column(Float, nullable=False, default=0.0)
    compute_hours = Column(Float, nullable=False, default=0.0)

    # Detailed metrics stored as JSON
    detailed_metrics = Column(JSON, nullable=False, default=dict)

    # Billing information
    billable_amount = Column(Float, nullable=True)
    currency = Column(String(3), nullable=False, default="USD")

    # Relationships
    organization = relationship("Organization", foreign_keys=[organization_id])

    __table_args__ = (
        UniqueConstraint('organization_id', 'period_start', 'period_end',
                        name='uq_org_usage_period'),
    )

    def __repr__(self):
        return f"<OrganizationUsage(org_id={self.organization_id}, period={self.period_start})>"