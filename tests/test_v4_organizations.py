"""
Tests for v4 organization management API endpoints.

This module contains tests for the enterprise organization management
functionality including CRUD operations, invitations, and tenant management.
"""

import pytest
from datetime import datetime, timedelta
from uuid import uuid4

from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from api.main import app
from api.models.organization import Organization, OrganizationInvitation
from api.schemas.organization import (
    OrganizationCreate,
    OrganizationSettings,
    SecuritySettings,
    PasswordPolicy,
    ContactInfo
)


@pytest.fixture
def client():
    """Create test client."""
    return TestClient(app)


@pytest.fixture
def sample_organization_data():
    """Create sample organization data for testing."""
    return {
        "name": "test-org",
        "display_name": "Test Organization",
        "description": "A test organization for unit testing",
        "subscription_tier": "professional",
        "settings": {
            "max_users": 50,
            "max_campaigns": 25,
            "max_assessments": 100,
            "data_retention_days": 365,
            "allowed_integrations": ["siem", "soar"],
            "security_settings": {
                "enforce_mfa": True,
                "session_timeout_minutes": 480,
                "password_policy": {
                    "min_length": 8,
                    "require_uppercase": True,
                    "require_lowercase": True,
                    "require_numbers": True,
                    "require_special_chars": True,
                    "max_age_days": 90
                },
                "ip_whitelist": [],
                "sso_required": False,
                "audit_log_retention_days": 2555
            },
            "branding": {
                "logo_url": "https://example.com/logo.png",
                "primary_color": "#007bff",
                "secondary_color": "#6c757d"
            }
        },
        "contact_info": {
            "primary_email": "<EMAIL>",
            "phone": "******-0123",
            "address": "123 Test Street",
            "city": "Test City",
            "state": "TS",
            "country": "US",
            "postal_code": "12345"
        },
        "compliance_requirements": [
            {
                "framework": "SOC2",
                "version": "2017",
                "required_controls": ["CC1.1", "CC1.2"],
                "audit_frequency": "annual"
            }
        ]
    }


class TestOrganizationAPI:
    """Test cases for organization management API."""
    
    def test_create_organization_success(self, client, sample_organization_data):
        """Test successful organization creation."""
        # Note: This test would need proper authentication setup
        # For now, we'll test the endpoint structure
        
        response = client.post(
            "/api/v4/organizations",
            json=sample_organization_data
        )
        
        # Since we don't have auth setup in this test, expect 401 or similar
        # In a real test environment, this would be 201 with proper auth
        assert response.status_code in [401, 403, 422]  # Expected without auth
    
    def test_list_organizations(self, client):
        """Test organization listing endpoint."""
        response = client.get("/api/v4/organizations")
        
        # Without auth, expect 401 or 403
        assert response.status_code in [401, 403]
    
    def test_get_organization_by_id(self, client):
        """Test getting organization by ID."""
        org_id = str(uuid4())
        response = client.get(f"/api/v4/organizations/{org_id}")
        
        # Without auth, expect 401 or 403
        assert response.status_code in [401, 403]
    
    def test_update_organization(self, client):
        """Test organization update endpoint."""
        org_id = str(uuid4())
        update_data = {
            "display_name": "Updated Organization Name",
            "description": "Updated description"
        }
        
        response = client.put(
            f"/api/v4/organizations/{org_id}",
            json=update_data
        )
        
        # Without auth, expect 401 or 403
        assert response.status_code in [401, 403]
    
    def test_delete_organization(self, client):
        """Test organization deletion endpoint."""
        org_id = str(uuid4())
        response = client.delete(f"/api/v4/organizations/{org_id}")
        
        # Without auth, expect 401 or 403
        assert response.status_code in [401, 403]
    
    def test_invite_user_to_organization(self, client):
        """Test user invitation endpoint."""
        org_id = str(uuid4())
        invitation_data = {
            "email": "<EMAIL>",
            "role": "member",
            "expires_in_days": 7
        }
        
        response = client.post(
            f"/api/v4/organizations/{org_id}/users",
            json=invitation_data
        )
        
        # Without auth, expect 401 or 403
        assert response.status_code in [401, 403]
    
    def test_remove_user_from_organization(self, client):
        """Test user removal endpoint."""
        org_id = str(uuid4())
        user_id = str(uuid4())
        
        response = client.delete(f"/api/v4/organizations/{org_id}/users/{user_id}")
        
        # Without auth, expect 401 or 403
        assert response.status_code in [401, 403]


class TestOrganizationSchemas:
    """Test cases for organization Pydantic schemas."""
    
    def test_organization_create_schema_validation(self, sample_organization_data):
        """Test OrganizationCreate schema validation."""
        # Test valid data
        org_create = OrganizationCreate(**sample_organization_data)
        assert org_create.name == "test-org"
        assert org_create.display_name == "Test Organization"
        assert org_create.subscription_tier == "professional"
    
    def test_organization_create_schema_validation_errors(self):
        """Test OrganizationCreate schema validation errors."""
        # Test missing required fields
        with pytest.raises(ValueError):
            OrganizationCreate(name="")  # Empty name should fail
        
        # Test invalid subscription tier
        with pytest.raises(ValueError):
            OrganizationCreate(
                name="test",
                display_name="Test",
                subscription_tier="invalid_tier",
                settings={},
                contact_info={}
            )
    
    def test_security_settings_schema(self):
        """Test SecuritySettings schema validation."""
        password_policy = PasswordPolicy(
            min_length=8,
            require_uppercase=True,
            require_lowercase=True,
            require_numbers=True,
            require_special_chars=True,
            max_age_days=90
        )
        
        security_settings = SecuritySettings(
            enforce_mfa=True,
            session_timeout_minutes=480,
            password_policy=password_policy,
            ip_whitelist=["***********/24"],
            sso_required=False,
            audit_log_retention_days=2555
        )
        
        assert security_settings.enforce_mfa is True
        assert security_settings.session_timeout_minutes == 480
        assert len(security_settings.ip_whitelist) == 1
    
    def test_contact_info_schema(self):
        """Test ContactInfo schema validation."""
        contact_info = ContactInfo(
            primary_email="<EMAIL>",
            phone="******-0123",
            address="123 Test St",
            city="Test City",
            state="TS",
            country="US",
            postal_code="12345"
        )
        
        assert contact_info.primary_email == "<EMAIL>"
        assert contact_info.phone == "******-0123"
        
        # Test invalid email
        with pytest.raises(ValueError):
            ContactInfo(primary_email="invalid-email")


class TestTenantAPI:
    """Test cases for tenant management API."""
    
    def test_get_tenant_resources(self, client):
        """Test tenant resource listing endpoint."""
        tenant_id = str(uuid4())
        response = client.get(f"/api/v4/tenants/{tenant_id}/resources")
        
        # Without auth, expect 401 or 403
        assert response.status_code in [401, 403]
    
    def test_migrate_tenant_data(self, client):
        """Test tenant migration endpoint."""
        tenant_id = str(uuid4())
        response = client.post(
            f"/api/v4/tenants/{tenant_id}/migrate",
            params={"target_region": "us-west-2"}
        )
        
        # Without auth, expect 401 or 403
        assert response.status_code in [401, 403]
    
    def test_get_tenant_usage_metrics(self, client):
        """Test tenant usage metrics endpoint."""
        tenant_id = str(uuid4())
        response = client.get(f"/api/v4/tenants/{tenant_id}/usage")
        
        # Without auth, expect 401 or 403
        assert response.status_code in [401, 403]
    
    def test_create_tenant_backup(self, client):
        """Test tenant backup creation endpoint."""
        tenant_id = str(uuid4())
        response = client.post(
            f"/api/v4/tenants/{tenant_id}/backup",
            params={"backup_type": "full"}
        )
        
        # Without auth, expect 401 or 403
        assert response.status_code in [401, 403]
    
    def test_restore_tenant_from_backup(self, client):
        """Test tenant restore endpoint."""
        tenant_id = str(uuid4())
        backup_id = str(uuid4())
        response = client.post(
            f"/api/v4/tenants/{tenant_id}/restore",
            params={"backup_id": backup_id}
        )
        
        # Without auth, expect 401 or 403
        assert response.status_code in [401, 403]
    
    def test_get_tenant_health_status(self, client):
        """Test tenant health status endpoint."""
        tenant_id = str(uuid4())
        response = client.get(f"/api/v4/tenants/{tenant_id}/health")
        
        # Without auth, expect 401 or 403
        assert response.status_code in [401, 403]


if __name__ == "__main__":
    pytest.main([__file__])
