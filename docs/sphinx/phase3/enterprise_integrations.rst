Enterprise Integrations
=======================

Phase 3 introduces comprehensive enterprise integration capabilities, enabling seamless connectivity with existing security infrastructure and identity management systems.

.. contents:: Table of Contents
   :local:
   :depth: 2

Overview
--------

The enterprise integrations module provides two main categories of integration:

1. **Single Sign-On (SSO) & Identity Providers**: Seamless authentication integration
2. **Enterprise Security Tools**: Direct connectivity with security platforms

These integrations enable organizations to leverage existing investments while extending capabilities through RegressionRigor's advanced features.

Single Sign-On (SSO) Integration
--------------------------------

SSO Provider Support
~~~~~~~~~~~~~~~~~~~

RegressionRigor supports all major SSO protocols and identity providers:

.. list-table:: Supported SSO Providers
   :header-rows: 1
   :widths: 20 30 50

   * - Protocol
     - Providers
     - Key Features
   * - SAML 2.0
     - Okta, Azure AD, ADFS, Ping Identity
     - Encrypted assertions, attribute mapping, SLO support
   * - OpenID Connect
     - Auth0, Google Workspace, Azure AD
     - JWT tokens, userinfo endpoint, refresh tokens
   * - OAuth 2.0
     - GitHub, GitLab, Custom providers
     - Authorization code flow, PKCE support
   * - LDAP
     - Active Directory, OpenLDAP
     - Secure LDAP, group membership, attribute sync
   * - Active Directory
     - Microsoft AD, Azure AD DS
     - Kerberos, NTLM, group policies

Configuration Examples
~~~~~~~~~~~~~~~~~~~~~

SAML 2.0 Configuration
^^^^^^^^^^^^^^^^^^^^^

.. code-block:: python

   from api.schemas.sso import SSOProviderCreate, SAML2Configuration, AttributeMapping

   # SAML2 Configuration
   saml2_config = SAML2Configuration(
       entity_id="urn:example:saml:entity",
       sso_url="https://idp.example.com/sso",
       slo_url="https://idp.example.com/slo",
       certificate="-----BEGIN CERTIFICATE-----\n...\n-----END CERTIFICATE-----",
       name_id_format="urn:oasis:names:tc:SAML:2.0:nameid-format:persistent",
       want_assertions_signed=True,
       want_response_signed=True,
       signature_algorithm="http://www.w3.org/2001/04/xmldsig-more#rsa-sha256"
   )

   # Attribute Mapping
   attr_mapping = AttributeMapping(
       email="mail",
       first_name="givenName",
       last_name="sn",
       username="uid",
       groups="memberOf",
       department="department",
       title="title"
   )

   # Create SSO Provider
   sso_provider = SSOProviderCreate(
       name="corporate-saml",
       display_name="Corporate SAML Provider",
       description="Main corporate identity provider",
       provider_type="saml2",
       configuration=saml2_config.dict(),
       attribute_mapping=attr_mapping,
       auto_provision_users=True,
       require_encrypted_assertions=True
   )

OpenID Connect Configuration
^^^^^^^^^^^^^^^^^^^^^^^^^^^

.. code-block:: python

   from api.schemas.sso import OIDCConfiguration

   # OIDC Configuration
   oidc_config = OIDCConfiguration(
       issuer="https://auth.example.com",
       client_id="regressionrigor-client",
       client_secret="your-client-secret",
       authorization_endpoint="https://auth.example.com/auth",
       token_endpoint="https://auth.example.com/token",
       userinfo_endpoint="https://auth.example.com/userinfo",
       jwks_uri="https://auth.example.com/.well-known/jwks.json",
       scopes=["openid", "profile", "email", "groups"]
   )

LDAP Configuration
^^^^^^^^^^^^^^^^^

.. code-block:: python

   from api.schemas.sso import LDAPConfiguration

   # LDAP Configuration
   ldap_config = LDAPConfiguration(
       server_url="ldaps://ldap.example.com:636",
       bind_dn="cn=service-account,ou=services,dc=example,dc=com",
       bind_password="service-password",
       base_dn="ou=users,dc=example,dc=com",
       user_filter="(uid={username})",
       user_attributes=["uid", "cn", "mail", "memberOf", "department"],
       use_ssl=True,
       use_start_tls=False
   )

SSO API Endpoints
~~~~~~~~~~~~~~~~

.. http:post:: /api/v4/sso/providers

   Create a new SSO provider configuration.

   **Request Body:**

   .. code-block:: json

      {
        "name": "corporate-saml",
        "display_name": "Corporate SAML Provider",
        "description": "Main corporate identity provider",
        "provider_type": "saml2",
        "configuration": {
          "entity_id": "urn:example:saml:entity",
          "sso_url": "https://idp.example.com/sso",
          "certificate": "-----BEGIN CERTIFICATE-----..."
        },
        "attribute_mapping": {
          "email": "mail",
          "first_name": "givenName",
          "last_name": "sn"
        },
        "auto_provision_users": true,
        "require_encrypted_assertions": true
      }

   **Response:**

   .. code-block:: json

      {
        "id": "550e8400-e29b-41d4-a716-************",
        "organization_id": "123e4567-e89b-12d3-a456-426614174000",
        "name": "corporate-saml",
        "display_name": "Corporate SAML Provider",
        "provider_type": "saml2",
        "status": "draft",
        "created_at": "2025-06-20T10:00:00Z"
      }

.. http:get:: /api/v4/sso/providers

   List all SSO providers for an organization.

   **Query Parameters:**

   * ``organization_id`` (required): Organization ID
   * ``provider_type`` (optional): Filter by provider type
   * ``status`` (optional): Filter by status
   * ``page`` (optional): Page number (default: 1)
   * ``size`` (optional): Page size (default: 20)

.. http:post:: /api/v4/sso/providers/{provider_id}/test

   Test SSO provider configuration.

   **Request Body:**

   .. code-block:: json

      {
        "test_type": "connection",
        "test_user": "<EMAIL>",
        "test_password": "testpassword"
      }

   **Response:**

   .. code-block:: json

      {
        "success": true,
        "test_type": "connection",
        "message": "Connection test successful",
        "details": {
          "provider_type": "saml2",
          "response_time_ms": 245
        },
        "tested_at": "2025-06-20T10:30:00Z",
        "duration_ms": 245
      }

Enterprise Security Tool Integration
-----------------------------------

Supported Integration Types
~~~~~~~~~~~~~~~~~~~~~~~~~~

RegressionRigor provides native connectors for major enterprise security tool categories:

.. list-table:: Enterprise Security Tool Categories
   :header-rows: 1
   :widths: 25 35 40

   * - Category
     - Example Tools
     - Integration Capabilities
   * - SIEM
     - Splunk, QRadar, ArcSight, Sentinel
     - Log ingestion, alert correlation, dashboard sync
   * - SOAR
     - Phantom, Demisto, Resilient
     - Playbook automation, case management, response orchestration
   * - TIP
     - ThreatConnect, Anomali, MISP
     - Threat intelligence feeds, IOC management, attribution
   * - Vulnerability Scanners
     - Nessus, Qualys, Rapid7, OpenVAS
     - Scan results import, vulnerability correlation, remediation tracking
   * - EDR
     - CrowdStrike, SentinelOne, Carbon Black
     - Endpoint telemetry, threat hunting, incident response
   * - Network Security
     - Palo Alto, Fortinet, Cisco ASA
     - Traffic analysis, policy management, threat detection

Integration Configuration
~~~~~~~~~~~~~~~~~~~~~~~~

HTTP-Based Integration Example
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

.. code-block:: python

   from api.schemas.integrations import (
       EnterpriseIntegrationCreate,
       HTTPConnectionConfig,
       APIKeyAuthConfig,
       DataMappingRule,
       SyncConfig
   )

   # Connection Configuration
   connection_config = HTTPConnectionConfig(
       base_url="https://api.splunk.example.com",
       timeout_seconds=30,
       verify_ssl=True,
       headers={"User-Agent": "RegressionRigor/1.0"}
   )

   # Authentication Configuration
   auth_config = APIKeyAuthConfig(
       api_key="your-splunk-api-key",
       header_name="Authorization"
   )

   # Data Mapping Rules
   data_mappings = [
       DataMappingRule(
           source_field="event_id",
           target_field="external_event_id",
           field_type="string",
           is_required=True,
           direction="bidirectional"
       ),
       DataMappingRule(
           source_field="severity",
           target_field="risk_level",
           field_type="string",
           transformation_rules={
               "mapping": {"low": "info", "medium": "warning", "high": "critical"}
           }
       )
   ]

   # Sync Configuration
   sync_config = SyncConfig(
       auto_sync=True,
       sync_interval_minutes=60,
       batch_size=100,
       retry_attempts=3,
       retry_delay_seconds=60
   )

   # Create Integration
   integration = EnterpriseIntegrationCreate(
       name="splunk-siem",
       display_name="Splunk SIEM Integration",
       description="Primary SIEM integration for log analysis",
       integration_type="siem",
       vendor="Splunk",
       product="Splunk Enterprise",
       version="8.2",
       connection_config=connection_config.dict(),
       auth_config=auth_config.dict(),
       data_mapping=data_mappings,
       sync_config=sync_config
   )

Database Integration Example
^^^^^^^^^^^^^^^^^^^^^^^^^^^

.. code-block:: python

   from api.schemas.integrations import DatabaseConnectionConfig, BasicAuthConfig

   # Database Connection
   db_config = DatabaseConnectionConfig(
       host="db.security.example.com",
       port=5432,
       database="security_db",
       username="readonly_user",
       password="secure_password",
       ssl_mode="require"
   )

   # Basic Authentication
   basic_auth = BasicAuthConfig(
       username="api_user",
       password="api_password"
   )

Integration API Endpoints
~~~~~~~~~~~~~~~~~~~~~~~~~

.. http:post:: /api/v4/integrations/enterprise

   Create a new enterprise integration.

   **Request Body:**

   .. code-block:: json

      {
        "name": "splunk-siem",
        "display_name": "Splunk SIEM Integration",
        "description": "Primary SIEM integration",
        "integration_type": "siem",
        "vendor": "Splunk",
        "product": "Splunk Enterprise",
        "version": "8.2",
        "connection_config": {
          "base_url": "https://api.splunk.example.com",
          "timeout_seconds": 30,
          "verify_ssl": true
        },
        "auth_config": {
          "api_key": "your-api-key",
          "header_name": "Authorization"
        },
        "data_mapping": [
          {
            "source_field": "event_id",
            "target_field": "external_event_id",
            "field_type": "string",
            "is_required": true
          }
        ],
        "sync_config": {
          "auto_sync": true,
          "sync_interval_minutes": 60,
          "batch_size": 100
        }
      }

.. http:post:: /api/v4/integrations/enterprise/{integration_id}/sync

   Initiate data synchronization with an enterprise integration.

   **Request Body:**

   .. code-block:: json

      {
        "job_type": "incremental_sync",
        "sync_parameters": {
          "last_sync": "2025-06-20T10:00:00Z",
          "include_deleted": false
        }
      }

   **Response:**

   .. code-block:: json

      {
        "id": "sync-job-123",
        "integration_id": "integration-456",
        "job_type": "incremental_sync",
        "status": "running",
        "started_at": "2025-06-20T11:00:00Z",
        "estimated_completion": "2025-06-20T11:15:00Z"
      }

Data Mapping Framework
---------------------

Transformation Rules
~~~~~~~~~~~~~~~~~~~

The data mapping framework supports sophisticated data transformations:

.. code-block:: python

   # Value Mapping
   transformation_rules = {
       "type": "value_mapping",
       "mapping": {
           "critical": "high",
           "major": "medium",
           "minor": "low"
       },
       "default_value": "unknown"
   }

   # Regular Expression Transformation
   regex_transformation = {
       "type": "regex",
       "pattern": r"(\d{4}-\d{2}-\d{2})",
       "replacement": r"\1T00:00:00Z",
       "flags": ["IGNORECASE"]
   }

   # Custom Function
   custom_transformation = {
       "type": "function",
       "function_name": "normalize_severity",
       "parameters": {
           "scale": "1-10",
           "output_format": "numeric"
       }
   }

Validation Rules
~~~~~~~~~~~~~~~

Data validation ensures integrity during synchronization:

.. code-block:: python

   validation_rules = {
       "required": True,
       "type": "string",
       "min_length": 1,
       "max_length": 255,
       "pattern": r"^[A-Z0-9-]+$",
       "custom_validators": [
           {
               "name": "unique_check",
               "parameters": {"scope": "organization"}
           }
       ]
   }

Best Practices
--------------

SSO Implementation
~~~~~~~~~~~~~~~~~

1. **Security First**: Always use encrypted assertions and signed responses
2. **Attribute Mapping**: Map all necessary user attributes for proper provisioning
3. **Testing**: Thoroughly test SSO configuration before production deployment
4. **Monitoring**: Implement monitoring for SSO authentication failures
5. **Backup Authentication**: Maintain local admin accounts for emergency access

Enterprise Integration
~~~~~~~~~~~~~~~~~~~~~

1. **Incremental Sync**: Use incremental synchronization to minimize resource usage
2. **Error Handling**: Implement robust error handling and retry mechanisms
3. **Data Validation**: Validate all incoming data before processing
4. **Rate Limiting**: Respect API rate limits of external systems
5. **Monitoring**: Monitor integration health and performance metrics

Troubleshooting
---------------

Common SSO Issues
~~~~~~~~~~~~~~~~

.. list-table:: SSO Troubleshooting Guide
   :header-rows: 1
   :widths: 30 70

   * - Issue
     - Solution
   * - Certificate validation errors
     - Verify certificate format and validity period
   * - Attribute mapping failures
     - Check attribute names and case sensitivity
   * - Authentication timeouts
     - Increase timeout values and check network connectivity
   * - User provisioning failures
     - Verify attribute mapping and user creation permissions

Common Integration Issues
~~~~~~~~~~~~~~~~~~~~~~~~

.. list-table:: Integration Troubleshooting Guide
   :header-rows: 1
   :widths: 30 70

   * - Issue
     - Solution
   * - Connection timeouts
     - Check network connectivity and firewall rules
   * - Authentication failures
     - Verify API keys and authentication configuration
   * - Data mapping errors
     - Review field mappings and transformation rules
   * - Sync job failures
     - Check logs for specific error messages and retry

Next Steps
----------

* :doc:`compliance_governance` - Implement compliance frameworks
* :doc:`scalability_performance` - Configure performance monitoring
* :doc:`implementation_guide` - Complete implementation guide
