Compliance & Governance
=======================

Phase 3 introduces comprehensive compliance and governance capabilities, enabling organizations to manage regulatory requirements, audit trails, and policy enforcement across their security testing operations.

.. contents:: Table of Contents
   :local:
   :depth: 2

Overview
--------

The compliance and governance module provides:

* **Multi-Framework Support**: Native support for SOC2, ISO27001, NIST CSF, PCI DSS, HIPAA, GDPR
* **Automated Assessments**: Streamlined compliance assessment workflows
* **Audit Trail**: Complete audit logging with tamper-evident storage
* **Policy Engine**: Flexible governance policy definition and enforcement
* **Reporting**: Comprehensive compliance reporting and gap analysis

Supported Compliance Frameworks
-------------------------------

.. list-table:: Compliance Framework Support
   :header-rows: 1
   :widths: 20 30 50

   * - Framework
     - Version
     - Key Features
   * - SOC 2
     - Type I & II
     - Trust services criteria, control testing, continuous monitoring
   * - ISO 27001
     - 2013/2022
     - Information security management, risk assessment, control implementation
   * - NIST CSF
     - 2.0
     - Cybersecurity framework, maturity assessment, implementation guidance
   * - PCI DSS
     - 4.0
     - Payment card security, network segmentation, vulnerability management
   * - HIPAA
     - Current
     - Healthcare data protection, access controls, audit logging
   * - GDPR
     - Current
     - Data privacy, consent management, breach notification

Compliance Framework API
------------------------

Framework Management
~~~~~~~~~~~~~~~~~~~

.. http:post:: /api/v4/compliance/frameworks

   Add a compliance framework to an organization.

   **Request Body:**

   .. code-block:: json

      {
        "name": "SOC 2 Type II",
        "framework_type": "soc2",
        "version": "2017",
        "description": "SOC 2 Type II compliance framework",
        "requirements": [
          {
            "id": "CC1.1",
            "title": "Control Environment",
            "description": "The entity demonstrates a commitment to integrity and ethical values",
            "category": "Common Criteria",
            "severity": "high",
            "test_procedures": [
              "Review code of conduct",
              "Interview management",
              "Test control implementation"
            ],
            "evidence_requirements": [
              "Code of conduct document",
              "Management interviews",
              "Control testing results"
            ]
          }
        ],
        "assessment_criteria": [
          {
            "criterion_id": "design_effectiveness",
            "name": "Design Effectiveness",
            "description": "Controls are designed effectively",
            "weight": 0.5
          }
        ]
      }

.. http:get:: /api/v4/compliance/frameworks

   List compliance frameworks for an organization.

   **Query Parameters:**

   * ``organization_id`` (required): Organization ID
   * ``framework_type`` (optional): Filter by framework type
   * ``active_only`` (optional): Show only active frameworks

Compliance Assessment API
~~~~~~~~~~~~~~~~~~~~~~~~~

.. http:post:: /api/v4/compliance/assessments

   Create a new compliance assessment.

   **Request Body:**

   .. code-block:: json

      {
        "framework_id": "550e8400-e29b-41d4-a716-446655440000",
        "assessment_name": "Q2 2025 SOC 2 Assessment",
        "assessment_type": "internal",
        "scope": {
          "systems": ["production", "staging"],
          "processes": ["user_access", "data_processing"],
          "time_period": {
            "start": "2025-04-01T00:00:00Z",
            "end": "2025-06-30T23:59:59Z"
          }
        },
        "assessor": "Internal Audit Team",
        "due_date": "2025-07-15T00:00:00Z"
      }

   **Response:**

   .. code-block:: json

      {
        "id": "assessment-123",
        "framework_id": "550e8400-e29b-41d4-a716-446655440000",
        "assessment_name": "Q2 2025 SOC 2 Assessment",
        "status": "in_progress",
        "progress": {
          "total_requirements": 64,
          "completed_requirements": 0,
          "passed_requirements": 0,
          "failed_requirements": 0
        },
        "created_at": "2025-06-20T10:00:00Z",
        "due_date": "2025-07-15T00:00:00Z"
      }

Audit & Governance API
----------------------

Audit Logging
~~~~~~~~~~~~~

.. http:get:: /api/v4/audit/logs

   Retrieve audit logs with filtering and search capabilities.

   **Query Parameters:**

   * ``organization_id`` (required): Organization ID
   * ``start_date`` (optional): Start date for log retrieval
   * ``end_date`` (optional): End date for log retrieval
   * ``user_id`` (optional): Filter by specific user
   * ``action`` (optional): Filter by action type
   * ``resource_type`` (optional): Filter by resource type
   * ``risk_level`` (optional): Filter by risk level

   **Response:**

   .. code-block:: json

      {
        "logs": [
          {
            "id": "log-123",
            "timestamp": "2025-06-20T10:30:00Z",
            "user_id": "user-456",
            "organization_id": "org-789",
            "action": "sso_provider_created",
            "resource_type": "sso_provider",
            "resource_id": "sso-provider-123",
            "ip_address": "*************",
            "user_agent": "Mozilla/5.0...",
            "details": {
              "provider_name": "okta-saml",
              "provider_type": "saml2"
            },
            "risk_level": "medium"
          }
        ],
        "total": 1,
        "page": 1,
        "size": 20
      }

Governance Policies
~~~~~~~~~~~~~~~~~~

.. http:post:: /api/v4/governance/policies

   Create a governance policy.

   **Request Body:**

   .. code-block:: json

      {
        "name": "Data Retention Policy",
        "policy_type": "data_retention",
        "description": "Defines data retention requirements for compliance",
        "rules": [
          {
            "rule_id": "retention_period",
            "condition": "data_type == 'assessment_results'",
            "action": "retain",
            "parameters": {
              "retention_days": 2555,
              "archive_after_days": 365
            }
          }
        ],
        "enforcement_level": "mandatory",
        "applicable_roles": ["admin", "compliance_officer"],
        "effective_date": "2025-07-01T00:00:00Z"
      }

Policy Violation Reporting
~~~~~~~~~~~~~~~~~~~~~~~~~

.. http:post:: /api/v4/governance/violations

   Report a policy violation.

   **Request Body:**

   .. code-block:: json

      {
        "policy_id": "policy-123",
        "violation_type": "data_retention_exceeded",
        "description": "Assessment data retained beyond policy limit",
        "severity": "medium",
        "affected_resources": [
          {
            "resource_type": "assessment",
            "resource_id": "assessment-456",
            "violation_details": "Data retained for 2600 days, exceeds 2555 day limit"
          }
        ],
        "remediation_plan": "Archive or delete data within 30 days",
        "reported_by": "automated_system"
      }

Implementation Examples
----------------------

SOC 2 Compliance Setup
~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   # Example: Set up SOC 2 Type II compliance framework
   
   from api.schemas.compliance import (
       ComplianceFrameworkCreate,
       ComplianceRequirement,
       AssessmentCriterion
   )
   
   # Define SOC 2 requirements
   soc2_requirements = [
       ComplianceRequirement(
           id="CC1.1",
           title="Control Environment - Integrity and Ethical Values",
           description="The entity demonstrates a commitment to integrity and ethical values",
           category="Common Criteria",
           severity="high",
           test_procedures=[
               "Review code of conduct and ethics policies",
               "Interview management about tone at the top",
               "Test implementation of ethical guidelines"
           ],
           evidence_requirements=[
               "Code of conduct document",
               "Ethics training records",
               "Management interview documentation"
           ],
           automation_possible=False
       ),
       ComplianceRequirement(
           id="CC6.1",
           title="Logical and Physical Access Controls",
           description="The entity implements logical and physical access controls",
           category="Common Criteria", 
           severity="high",
           test_procedures=[
               "Review access control policies",
               "Test user access provisioning",
               "Validate access reviews"
           ],
           evidence_requirements=[
               "Access control matrix",
               "User access reports",
               "Access review documentation"
           ],
           automation_possible=True
       )
   ]
   
   # Create SOC 2 framework
   soc2_framework = ComplianceFrameworkCreate(
       name="SOC 2 Type II",
       framework_type="soc2",
       version="2017",
       description="SOC 2 Type II compliance framework for service organizations",
       requirements=soc2_requirements,
       assessment_criteria=[
           AssessmentCriterion(
               criterion_id="design_effectiveness",
               name="Design Effectiveness",
               description="Controls are designed effectively to meet criteria",
               weight=0.4
           ),
           AssessmentCriterion(
               criterion_id="operating_effectiveness",
               name="Operating Effectiveness", 
               description="Controls operated effectively throughout the period",
               weight=0.6
           )
       ]
   )

Automated Compliance Monitoring
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   # Example: Automated compliance monitoring
   
   from api.services.compliance import ComplianceMonitoringService
   
   class AutomatedComplianceMonitor:
       """Automated compliance monitoring and alerting."""
       
       def __init__(self, db_session):
           self.db = db_session
           self.compliance_service = ComplianceMonitoringService(db_session)
       
       async def monitor_access_controls(self, organization_id: UUID):
           """Monitor access control compliance."""
           
           # Check for users without MFA
           users_without_mfa = await self.compliance_service.get_users_without_mfa(
               organization_id
           )
           
           if users_without_mfa:
               await self.compliance_service.create_compliance_finding(
                   organization_id=organization_id,
                   requirement_id="CC6.1",
                   finding_type="control_deficiency",
                   severity="medium",
                   description=f"{len(users_without_mfa)} users without MFA enabled",
                   affected_resources=users_without_mfa
               )
       
       async def monitor_data_retention(self, organization_id: UUID):
           """Monitor data retention compliance."""
           
           # Check for data exceeding retention periods
           expired_data = await self.compliance_service.get_expired_data(
               organization_id
           )
           
           if expired_data:
               await self.compliance_service.create_compliance_finding(
                   organization_id=organization_id,
                   requirement_id="DATA_RETENTION_001",
                   finding_type="policy_violation",
                   severity="high",
                   description="Data retained beyond policy limits",
                   affected_resources=expired_data
               )

Best Practices
--------------

Compliance Management
~~~~~~~~~~~~~~~~~~~

1. **Framework Selection**: Choose frameworks relevant to your industry and business
2. **Regular Assessments**: Conduct regular compliance assessments (quarterly/annually)
3. **Continuous Monitoring**: Implement automated monitoring for key controls
4. **Documentation**: Maintain comprehensive documentation of controls and evidence
5. **Training**: Provide regular compliance training to relevant staff

Audit Trail Management
~~~~~~~~~~~~~~~~~~~~

1. **Comprehensive Logging**: Log all significant actions and changes
2. **Tamper Protection**: Implement tamper-evident audit log storage
3. **Regular Reviews**: Conduct regular audit log reviews
4. **Retention Policies**: Implement appropriate audit log retention policies
5. **Access Controls**: Restrict access to audit logs to authorized personnel

Policy Enforcement
~~~~~~~~~~~~~~~~~

1. **Clear Policies**: Define clear, actionable governance policies
2. **Automated Enforcement**: Implement automated policy enforcement where possible
3. **Regular Reviews**: Review and update policies regularly
4. **Exception Management**: Implement proper exception handling processes
5. **Training and Communication**: Ensure policies are well-communicated and understood

Troubleshooting
---------------

Common Compliance Issues
~~~~~~~~~~~~~~~~~~~~~~

.. list-table:: Compliance Troubleshooting Guide
   :header-rows: 1
   :widths: 30 70

   * - Issue
     - Solution
   * - Assessment progress not updating
     - Check assessment workflow configuration and permissions
   * - Audit logs missing
     - Verify audit logging is enabled and properly configured
   * - Policy violations not detected
     - Review policy rules and automated monitoring configuration
   * - Compliance reports incomplete
     - Ensure all required evidence is collected and documented

Next Steps
----------

* :doc:`scalability_performance` - Configure performance monitoring and optimization
* :doc:`business_intelligence` - Set up business intelligence and analytics
* :doc:`implementation_guide` - Complete implementation guide
