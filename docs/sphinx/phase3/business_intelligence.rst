Business Intelligence & Analytics
==================================

Phase 3 introduces comprehensive business intelligence and analytics capabilities, providing executives and stakeholders with actionable insights into security posture, compliance status, and operational performance.

.. contents:: Table of Contents
   :local:
   :depth: 2

Overview
--------

The business intelligence module provides:

* **Executive Dashboards**: High-level KPI tracking and trend analysis
* **BI Dataset Management**: Flexible data modeling and query execution
* **Advanced Reporting**: Scheduled reports and custom analytics
* **Cross-Organizational Analytics**: Benchmarking and comparative analysis
* **Predictive Analytics**: Risk forecasting and trend prediction

Executive Dashboard
------------------

Key Performance Indicators
~~~~~~~~~~~~~~~~~~~~~~~~~

The executive dashboard tracks critical security and operational KPIs:

.. list-table:: Executive KPIs
   :header-rows: 1
   :widths: 25 25 50

   * - Category
     - KPI
     - Description
   * - Security Posture
     - Overall Security Score
     - Aggregated security posture across all frameworks
   * - Compliance
     - Compliance Coverage %
     - Percentage of compliance requirements met
   * - Risk Management
     - Critical Risks Open
     - Number of unresolved critical security risks
   * - Operational
     - Assessment Completion Rate
     - Percentage of assessments completed on time
   * - Integration
     - Integration Health Score
     - Health status of enterprise integrations
   * - User Adoption
     - Active User Growth
     - Month-over-month active user growth

Executive Dashboard API
~~~~~~~~~~~~~~~~~~~~~~

.. http:get:: /api/v4/executive/dashboard

   Get executive dashboard data for an organization.

   **Query Parameters:**

   * ``organization_id`` (required): Organization ID
   * ``time_period`` (optional): Time period for metrics (30d, 90d, 1y)
   * ``include_trends`` (optional): Include trend analysis

   **Response:**

   .. code-block:: json

      {
        "organization_id": "123e4567-e89b-12d3-a456-426614174000",
        "dashboard_date": "2025-06-20T10:00:00Z",
        "time_period": "30d",
        "kpis": [
          {
            "name": "Overall Security Score",
            "current_value": 87.5,
            "target_value": 90.0,
            "trend": "improving",
            "change_percent": 2.3,
            "status": "on_track",
            "last_updated": "2025-06-20T09:00:00Z",
            "unit": "percentage"
          },
          {
            "name": "Compliance Coverage",
            "current_value": 94.2,
            "target_value": 95.0,
            "trend": "stable",
            "change_percent": 0.1,
            "status": "at_risk",
            "last_updated": "2025-06-20T08:30:00Z",
            "unit": "percentage"
          }
        ],
        "risk_summary": {
          "total_risks": 45,
          "critical_risks": 2,
          "high_risks": 8,
          "medium_risks": 20,
          "low_risks": 15,
          "risk_trend": "decreasing"
        },
        "compliance_status": {
          "frameworks": [
            {
              "framework": "SOC 2",
              "compliance_percentage": 96.5,
              "status": "compliant",
              "next_assessment": "2025-09-15T00:00:00Z"
            },
            {
              "framework": "ISO 27001",
              "compliance_percentage": 91.8,
              "status": "minor_gaps",
              "next_assessment": "2025-08-01T00:00:00Z"
            }
          ]
        },
        "recommendations": [
          {
            "priority": "high",
            "category": "compliance",
            "title": "Address ISO 27001 gaps",
            "description": "3 control gaps identified in access management",
            "estimated_effort": "2 weeks",
            "impact": "Improve compliance score by 3.2%"
          }
        ]
      }

.. http:post:: /api/v4/executive/kpis

   Define custom executive KPIs.

   **Request Body:**

   .. code-block:: json

      {
        "name": "Mean Time to Remediation",
        "description": "Average time to remediate critical security findings",
        "category": "operational_efficiency",
        "calculation": {
          "type": "average",
          "source_table": "security_findings",
          "value_field": "remediation_time_hours",
          "filters": [
            {"field": "severity", "operator": "equals", "value": "critical"},
            {"field": "status", "operator": "equals", "value": "resolved"}
          ]
        },
        "target_value": 24.0,
        "unit": "hours",
        "trend_analysis": true,
        "alert_thresholds": {
          "warning": 36.0,
          "critical": 48.0
        }
      }

Business Intelligence Datasets
------------------------------

Dataset Management
~~~~~~~~~~~~~~~~~

BI datasets provide structured access to organizational data for analysis:

.. code-block:: python

   # Example BI dataset definition
   
   from api.schemas.bi import BIDatasetCreate, DatasetSchema, TableSchema
   
   # Security metrics dataset
   security_dataset = BIDatasetCreate(
       name="security_metrics",
       description="Comprehensive security metrics and KPIs",
       data_sources=[
           "campaigns",
           "assessments", 
           "security_findings",
           "compliance_assessments"
       ],
       schema=DatasetSchema(
           tables=[
               TableSchema(
                   name="security_findings",
                   columns=[
                       {"name": "finding_id", "type": "string", "primary_key": True},
                       {"name": "severity", "type": "string"},
                       {"name": "status", "type": "string"},
                       {"name": "created_date", "type": "datetime"},
                       {"name": "remediation_date", "type": "datetime"},
                       {"name": "framework", "type": "string"}
                   ]
               ),
               TableSchema(
                   name="compliance_status",
                   columns=[
                       {"name": "framework", "type": "string"},
                       {"name": "requirement_id", "type": "string"},
                       {"name": "compliance_status", "type": "string"},
                       {"name": "last_assessed", "type": "datetime"}
                   ]
               )
           ],
           relationships=[
               {
                   "from_table": "security_findings",
                   "from_column": "framework",
                   "to_table": "compliance_status", 
                   "to_column": "framework",
                   "relationship_type": "many_to_one"
               }
           ]
       ),
       refresh_schedule={
           "frequency": "hourly",
           "time_zone": "UTC"
       }
   )

BI Dataset API
~~~~~~~~~~~~~

.. http:post:: /api/v4/bi/datasets

   Create a new BI dataset.

   **Request Body:**

   .. code-block:: json

      {
        "name": "security_metrics",
        "description": "Security metrics and KPIs dataset",
        "data_sources": ["campaigns", "assessments", "findings"],
        "schema": {
          "tables": [
            {
              "name": "findings_summary",
              "columns": [
                {"name": "date", "type": "date"},
                {"name": "severity", "type": "string"},
                {"name": "count", "type": "integer"}
              ]
            }
          ]
        },
        "refresh_schedule": {
          "frequency": "daily",
          "time": "02:00",
          "time_zone": "UTC"
        },
        "access_permissions": [
          {
            "role": "executive",
            "permissions": ["read", "query"]
          },
          {
            "role": "security_analyst",
            "permissions": ["read", "query", "export"]
          }
        ]
      }

.. http:post:: /api/v4/bi/queries

   Execute a BI query against a dataset.

   **Request Body:**

   .. code-block:: json

      {
        "dataset_id": "dataset-123",
        "query": "SELECT severity, COUNT(*) as count FROM security_findings WHERE created_date >= '2025-05-01' GROUP BY severity",
        "parameters": {
          "start_date": "2025-05-01",
          "organization_id": "org-123"
        },
        "result_format": "json",
        "cache_ttl_seconds": 300
      }

   **Response:**

   .. code-block:: json

      {
        "query_id": "query-456",
        "execution_time_ms": 245,
        "row_count": 4,
        "columns": ["severity", "count"],
        "data": [
          {"severity": "critical", "count": 12},
          {"severity": "high", "count": 34},
          {"severity": "medium", "count": 67},
          {"severity": "low", "count": 123}
        ],
        "cached": false,
        "cache_expires_at": "2025-06-20T10:05:00Z"
      }

Advanced Reporting
-----------------

Scheduled Reports
~~~~~~~~~~~~~~~~

.. http:post:: /api/v4/bi/reports/schedule

   Schedule automated report generation.

   **Request Body:**

   .. code-block:: json

      {
        "name": "Monthly Security Executive Report",
        "description": "Comprehensive monthly security posture report",
        "report_type": "executive_summary",
        "schedule": {
          "frequency": "monthly",
          "day_of_month": 1,
          "time": "08:00",
          "time_zone": "America/New_York"
        },
        "parameters": {
          "time_period": "previous_month",
          "include_trends": true,
          "include_recommendations": true
        },
        "delivery": {
          "method": "email",
          "recipients": [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>"
          ],
          "format": "pdf",
          "subject_template": "Monthly Security Report - {{month}} {{year}}"
        },
        "enabled": true
      }

Custom Analytics
~~~~~~~~~~~~~~~

.. code-block:: python

   # Example custom analytics implementation
   
   class SecurityAnalytics:
       """Custom security analytics and insights."""
       
       def __init__(self, bi_service):
           self.bi = bi_service
       
       async def calculate_security_trend(self, organization_id: UUID, days: int = 90):
           """Calculate security posture trend over time."""
           
           query = """
           SELECT 
               DATE_TRUNC('week', assessment_date) as week,
               AVG(security_score) as avg_score,
               COUNT(*) as assessment_count
           FROM security_assessments 
           WHERE organization_id = %(org_id)s 
               AND assessment_date >= NOW() - INTERVAL '%(days)s days'
           GROUP BY week
           ORDER BY week
           """
           
           result = await self.bi.execute_query(
               query, 
               parameters={'org_id': organization_id, 'days': days}
           )
           
           # Calculate trend
           scores = [row['avg_score'] for row in result['data']]
           trend = self._calculate_trend(scores)
           
           return {
               'trend_direction': trend['direction'],
               'trend_strength': trend['strength'],
               'current_score': scores[-1] if scores else 0,
               'score_change': scores[-1] - scores[0] if len(scores) > 1 else 0,
               'weekly_data': result['data']
           }
       
       async def risk_heat_map(self, organization_id: UUID):
           """Generate risk heat map data."""
           
           query = """
           SELECT 
               framework,
               category,
               COUNT(*) as risk_count,
               AVG(CASE 
                   WHEN severity = 'critical' THEN 4
                   WHEN severity = 'high' THEN 3
                   WHEN severity = 'medium' THEN 2
                   WHEN severity = 'low' THEN 1
                   ELSE 0
               END) as avg_severity_score
           FROM security_findings
           WHERE organization_id = %(org_id)s
               AND status = 'open'
           GROUP BY framework, category
           """
           
           result = await self.bi.execute_query(
               query,
               parameters={'org_id': organization_id}
           )
           
           return self._format_heat_map_data(result['data'])

Cross-Organizational Analytics
-----------------------------

Benchmarking
~~~~~~~~~~~

.. http:get:: /api/v4/bi/benchmarks

   Get industry benchmarking data.

   **Query Parameters:**

   * ``organization_id`` (required): Organization ID
   * ``industry`` (optional): Industry sector for comparison
   * ``company_size`` (optional): Company size category
   * ``metrics`` (optional): Specific metrics to benchmark

   **Response:**

   .. code-block:: json

      {
        "organization_id": "org-123",
        "benchmark_date": "2025-06-20T10:00:00Z",
        "industry": "financial_services",
        "company_size": "large",
        "benchmarks": [
          {
            "metric": "overall_security_score",
            "organization_value": 87.5,
            "industry_average": 82.3,
            "industry_median": 84.1,
            "percentile": 75,
            "status": "above_average"
          },
          {
            "metric": "compliance_coverage",
            "organization_value": 94.2,
            "industry_average": 89.7,
            "industry_median": 91.2,
            "percentile": 68,
            "status": "above_average"
          }
        ],
        "insights": [
          {
            "type": "strength",
            "message": "Your security score is 6.3% above industry average"
          },
          {
            "type": "opportunity",
            "message": "Consider improving incident response time (currently 32% below industry median)"
          }
        ]
      }

Predictive Analytics
-------------------

Risk Forecasting
~~~~~~~~~~~~~~~

.. code-block:: python

   # Predictive risk analytics
   
   class RiskForecasting:
       """Predictive analytics for risk management."""
       
       def __init__(self, ml_service):
           self.ml = ml_service
       
       async def forecast_security_risks(self, organization_id: UUID, forecast_days: int = 30):
           """Forecast potential security risks."""
           
           # Gather historical data
           historical_data = await self._gather_risk_data(organization_id, days=365)
           
           # Apply machine learning model
           forecast = await self.ml.predict_risks(
               data=historical_data,
               forecast_horizon=forecast_days
           )
           
           return {
               'forecast_period_days': forecast_days,
               'predicted_risks': [
                   {
                       'risk_type': 'configuration_drift',
                       'probability': 0.73,
                       'estimated_impact': 'medium',
                       'predicted_date': '2025-07-05T00:00:00Z',
                       'confidence': 0.85
                   },
                   {
                       'risk_type': 'compliance_gap',
                       'probability': 0.45,
                       'estimated_impact': 'high',
                       'predicted_date': '2025-07-12T00:00:00Z',
                       'confidence': 0.72
                   }
               ],
               'recommendations': [
                   {
                       'action': 'Increase configuration monitoring frequency',
                       'rationale': 'High probability of configuration drift detected',
                       'priority': 'high'
                   }
               ]
           }

Implementation Examples
----------------------

Executive Dashboard Setup
~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   # Example executive dashboard configuration
   
   from api.services.bi import ExecutiveDashboardService
   
   async def setup_executive_dashboard(organization_id: UUID):
       """Set up executive dashboard for an organization."""
       
       dashboard_service = ExecutiveDashboardService()
       
       # Define KPIs
       kpis = [
           {
               'name': 'Overall Security Score',
               'calculation': 'weighted_average',
               'sources': ['nist_csf_score', 'iso27001_score', 'soc2_score'],
               'target': 90.0,
               'weight': 0.4
           },
           {
               'name': 'Critical Findings Open',
               'calculation': 'count',
               'source': 'security_findings',
               'filters': {'severity': 'critical', 'status': 'open'},
               'target': 0,
               'weight': 0.3
           },
           {
               'name': 'Assessment Completion Rate',
               'calculation': 'percentage',
               'numerator': 'completed_assessments',
               'denominator': 'total_assessments',
               'target': 95.0,
               'weight': 0.3
           }
       ]
       
       # Create dashboard
       dashboard = await dashboard_service.create_dashboard(
           organization_id=organization_id,
           kpis=kpis,
           refresh_interval_minutes=60
       )
       
       return dashboard

Best Practices
--------------

Dashboard Design
~~~~~~~~~~~~~~~

1. **Focus on Outcomes**: Display metrics that drive business decisions
2. **Visual Hierarchy**: Use clear visual hierarchy to highlight important information
3. **Real-Time Updates**: Ensure dashboards reflect current state
4. **Mobile Responsive**: Design for mobile and tablet viewing
5. **Contextual Information**: Provide context and explanations for metrics

Data Quality
~~~~~~~~~~~

1. **Data Validation**: Implement comprehensive data validation
2. **Data Lineage**: Track data sources and transformations
3. **Refresh Schedules**: Implement appropriate data refresh schedules
4. **Error Handling**: Handle data quality issues gracefully
5. **Audit Trail**: Maintain audit trail for data changes

Performance Optimization
~~~~~~~~~~~~~~~~~~~~~~~

1. **Query Optimization**: Optimize BI queries for performance
2. **Caching Strategy**: Implement intelligent caching for BI data
3. **Incremental Updates**: Use incremental data updates where possible
4. **Resource Management**: Monitor and manage BI resource usage
5. **Scalability Planning**: Plan for growth in data volume and users

Next Steps
----------

* :doc:`implementation_guide` - Complete implementation and deployment guide
* :doc:`multi_tenancy` - Multi-tenancy and organization management
* :doc:`enterprise_integrations` - SSO and enterprise tool integrations
