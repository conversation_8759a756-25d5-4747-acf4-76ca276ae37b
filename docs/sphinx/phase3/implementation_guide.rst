Phase 3 Implementation Guide
============================

This comprehensive guide walks you through implementing Phase 3 enterprise features in your RegressionRigor deployment.

.. contents:: Table of Contents
   :local:
   :depth: 2

Prerequisites
-------------

System Requirements
~~~~~~~~~~~~~~~~~~

Before implementing Phase 3, ensure your system meets these requirements:

.. list-table:: System Requirements
   :header-rows: 1
   :widths: 25 75

   * - Component
     - Requirement
   * - Operating System
     - Linux (Ubuntu 20.04+ recommended), macOS, or Windows with WSL2
   * - Python
     - Python 3.11+ with pip and virtual environment support
   * - Database
     - PostgreSQL 13+ with UUID extension and JSON support
   * - Cache
     - Redis 6+ for session management and caching
   * - Memory
     - Minimum 8GB RAM (16GB+ recommended for production)
   * - Storage
     - Minimum 50GB available space (SSD recommended)
   * - Network
     - HTTPS/TLS support for secure communications

Software Dependencies
~~~~~~~~~~~~~~~~~~~~

Ensure these software components are installed:

.. code-block:: bash

   # Database
   sudo apt-get install postgresql-13 postgresql-contrib
   
   # Cache
   sudo apt-get install redis-server
   
   # Python dependencies (handled by requirements.txt)
   pip install -r requirements.txt
   
   # Additional Phase 3 dependencies
   pip install pydantic-settings
   pip install python-multipart
   pip install cryptography

Phase 1 & 2 Completion
~~~~~~~~~~~~~~~~~~~~~

Phase 3 builds upon the foundation established in previous phases:

* ✅ **Phase 1**: Core platform with MITRE ATT&CK, D3FEND, ATLAS, STIX integration
* ✅ **Phase 2**: Advanced analytics, ISF framework, NIST CSF 2.0 support
* ✅ **Database Migrations**: All previous migrations applied successfully
* ✅ **API Structure**: v1-v3 APIs functional and tested

Installation Steps
------------------

Step 1: Database Migration
~~~~~~~~~~~~~~~~~~~~~~~~~

Apply the Phase 3 database migration to add enterprise features:

.. code-block:: bash

   # Navigate to project directory
   cd /path/to/regression-rigor
   
   # Activate virtual environment
   source venv/bin/activate  # or your preferred method
   
   # Apply Phase 3 migration
   alembic upgrade head
   
   # Verify migration
   python -c "
   from api.database import engine
   from sqlalchemy import text
   with engine.connect() as conn:
       result = conn.execute(text('SELECT table_name FROM information_schema.tables WHERE table_schema = \\'public\\' AND table_name LIKE \\'%organization%\\';'))
       print('Organization tables:', [row[0] for row in result])
   "

Expected output should include tables like:
- ``organizations``
- ``organization_invitations``
- ``sso_providers``
- ``sso_auth_sessions``
- ``enterprise_integrations``

Step 2: Configuration Update
~~~~~~~~~~~~~~~~~~~~~~~~~~~

Update your configuration to include Phase 3 settings:

.. code-block:: python

   # api/core/config.py additions
   
   class Settings(BaseSettings):
       # ... existing settings ...
       
       # Phase 3 Enterprise Settings
       ENABLE_MULTI_TENANCY: bool = True
       DEFAULT_SUBSCRIPTION_TIER: str = "starter"
       MAX_ORGANIZATIONS_PER_INSTANCE: int = 1000
       
       # SSO Configuration
       SSO_SESSION_TIMEOUT_MINUTES: int = 480  # 8 hours
       SSO_METADATA_CACHE_TTL_SECONDS: int = 3600  # 1 hour
       
       # Integration Settings
       INTEGRATION_TIMEOUT_SECONDS: int = 30
       MAX_SYNC_BATCH_SIZE: int = 1000
       INTEGRATION_RETRY_ATTEMPTS: int = 3
       
       # Email Configuration (for invitations)
       SMTP_HOST: str = "localhost"
       SMTP_PORT: int = 587
       SMTP_USERNAME: str = ""
       SMTP_PASSWORD: str = ""
       SMTP_USE_TLS: bool = True
       FROM_EMAIL: str = "<EMAIL>"
       
       # Billing and Usage
       ENABLE_USAGE_TRACKING: bool = True
       BILLING_CALCULATION_TIMEZONE: str = "UTC"

Step 3: Environment Variables
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Set up environment variables for Phase 3:

.. code-block:: bash

   # .env file additions
   
   # Multi-tenancy
   ENABLE_MULTI_TENANCY=true
   DEFAULT_SUBSCRIPTION_TIER=starter
   
   # Email configuration
   SMTP_HOST=smtp.gmail.com
   SMTP_PORT=587
   SMTP_USERNAME=<EMAIL>
   SMTP_PASSWORD=your-app-password
   SMTP_USE_TLS=true
   FROM_EMAIL=<EMAIL>
   
   # Security
   SSO_SESSION_TIMEOUT_MINUTES=480
   
   # Performance
   INTEGRATION_TIMEOUT_SECONDS=30
   MAX_SYNC_BATCH_SIZE=1000

Step 4: API Router Registration
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Register the Phase 3 API routes in your main application:

.. code-block:: python

   # main.py or app.py
   
   from fastapi import FastAPI
   from api.routes.v4 import router as v4_router
   
   app = FastAPI(title="RegressionRigor Platform")
   
   # Register Phase 3 v4 API routes
   app.include_router(
       v4_router,
       prefix="/api/v4",
       tags=["v4"]
   )

Step 5: Database Initialization
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Initialize the database with default data:

.. code-block:: python

   # scripts/init_phase3.py
   
   from api.database import SessionLocal
   from api.models.organization import Organization, SubscriptionTier
   from api.services.organization import OrganizationService
   
   def init_default_organization():
       """Create a default organization for initial setup."""
       db = SessionLocal()
       try:
           service = OrganizationService(db)
           
           # Check if any organizations exist
           existing = db.query(Organization).first()
           if existing:
               print(f"Organizations already exist. Skipping initialization.")
               return
           
           # Create default organization
           from api.schemas.organization import OrganizationCreate, OrganizationSettings
           
           default_org = OrganizationCreate(
               name="default-org",
               display_name="Default Organization",
               description="Default organization for initial setup",
               subscription_tier=SubscriptionTier.ENTERPRISE,
               settings=OrganizationSettings(),
               contact_info={
                   "primary_contact": "admin@localhost",
                   "phone": "",
                   "address": {}
               },
               compliance_requirements=[]
           )
           
           org = service.create_organization(default_org, created_by=None)
           print(f"Created default organization: {org.id}")
           
       finally:
           db.close()
   
   if __name__ == "__main__":
       init_default_organization()

Run the initialization:

.. code-block:: bash

   python scripts/init_phase3.py

Configuration Examples
----------------------

SSO Provider Configuration
~~~~~~~~~~~~~~~~~~~~~~~~~

Example SAML2 provider setup:

.. code-block:: python

   # Example: Configure Okta SAML2 SSO
   
   import requests
   from api.schemas.sso import SSOProviderCreate, SAML2Configuration, AttributeMapping
   
   # SAML2 configuration for Okta
   saml2_config = SAML2Configuration(
       entity_id="http://www.okta.com/exk1234567890abcdef",
       sso_url="https://dev-123456.okta.com/app/dev-123456_regressionrigor_1/exk1234567890abcdef/sso/saml",
       slo_url="https://dev-123456.okta.com/app/dev-123456_regressionrigor_1/exk1234567890abcdef/slo/saml",
       certificate="""-----BEGIN CERTIFICATE-----
   MIIDpDCCAoygAwIBAgIGAV2ka+55MA0GCSqGSIb3DQEBCwUAMIGSMQswCQYDVQQG
   ... (certificate content) ...
   -----END CERTIFICATE-----""",
       name_id_format="urn:oasis:names:tc:SAML:1.1:nameid-format:emailAddress",
       want_assertions_signed=True,
       want_response_signed=True
   )
   
   # Attribute mapping
   attr_mapping = AttributeMapping(
       email="email",
       first_name="firstName",
       last_name="lastName",
       username="login",
       groups="groups"
   )
   
   # Create SSO provider
   sso_provider = SSOProviderCreate(
       name="okta-saml",
       display_name="Okta SAML SSO",
       description="Corporate Okta SAML integration",
       provider_type="saml2",
       configuration=saml2_config.dict(),
       attribute_mapping=attr_mapping,
       auto_provision_users=True,
       require_encrypted_assertions=True
   )
   
   # API call to create provider
   response = requests.post(
       "http://localhost:8000/api/v4/sso/providers",
       json=sso_provider.dict(),
       params={"organization_id": "your-org-id"},
       headers={"Authorization": "Bearer your-token"}
   )

Enterprise Integration Setup
~~~~~~~~~~~~~~~~~~~~~~~~~~~

Example Splunk SIEM integration:

.. code-block:: python

   # Example: Configure Splunk SIEM integration
   
   from api.schemas.integrations import (
       EnterpriseIntegrationCreate,
       HTTPConnectionConfig,
       APIKeyAuthConfig,
       DataMappingRule,
       SyncConfig
   )
   
   # Connection configuration
   connection_config = HTTPConnectionConfig(
       base_url="https://splunk.yourcompany.com:8089",
       timeout_seconds=30,
       verify_ssl=True,
       headers={
           "Content-Type": "application/json",
           "User-Agent": "RegressionRigor/1.0"
       }
   )
   
   # Authentication
   auth_config = APIKeyAuthConfig(
       api_key="your-splunk-token",
       header_name="Authorization"
   )
   
   # Data mapping
   data_mappings = [
       DataMappingRule(
           source_field="event_id",
           target_field="splunk_event_id",
           field_type="string",
           is_required=True,
           direction="bidirectional"
       ),
       DataMappingRule(
           source_field="severity",
           target_field="urgency",
           field_type="string",
           transformation_rules={
               "type": "value_mapping",
               "mapping": {
                   "low": "info",
                   "medium": "warning", 
                   "high": "critical"
               }
           }
       )
   ]
   
   # Sync configuration
   sync_config = SyncConfig(
       auto_sync=True,
       sync_interval_minutes=60,
       batch_size=100,
       retry_attempts=3,
       retry_delay_seconds=60
   )
   
   # Create integration
   integration = EnterpriseIntegrationCreate(
       name="splunk-siem",
       display_name="Splunk SIEM Integration",
       description="Primary SIEM for security event correlation",
       integration_type="siem",
       vendor="Splunk",
       product="Splunk Enterprise",
       version="8.2",
       connection_config=connection_config.dict(),
       auth_config=auth_config.dict(),
       data_mapping=data_mappings,
       sync_config=sync_config
   )

Testing and Validation
----------------------

Phase 3 Test Suite
~~~~~~~~~~~~~~~~~

Run the comprehensive Phase 3 test suite:

.. code-block:: bash

   # Run foundation tests
   python test_phase3_standalone.py
   
   # Run enterprise integrations tests
   python test_phase3_enterprise_standalone.py
   
   # Run organization API tests
   python -m pytest tests/test_v4_organizations.py -v

Expected output:
- ✅ All foundation tests should pass (4/4)
- ✅ All enterprise integration tests should pass (5/5)
- ✅ All organization API tests should pass

API Validation
~~~~~~~~~~~~~

Test the Phase 3 APIs manually:

.. code-block:: bash

   # Test organization creation
   curl -X POST "http://localhost:8000/api/v4/organizations" \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer your-token" \
        -d '{
          "name": "test-org",
          "display_name": "Test Organization",
          "subscription_tier": "starter",
          "contact_info": {"primary_contact": "<EMAIL>"},
          "settings": {"max_users": 10}
        }'
   
   # Test SSO provider listing
   curl -X GET "http://localhost:8000/api/v4/sso/providers?organization_id=your-org-id" \
        -H "Authorization: Bearer your-token"
   
   # Test integration listing
   curl -X GET "http://localhost:8000/api/v4/integrations/enterprise?organization_id=your-org-id" \
        -H "Authorization: Bearer your-token"

Next Steps
----------

After successful Phase 3 implementation:

1. **User Training**: Train administrators on organization management
2. **SSO Rollout**: Gradually migrate users to SSO authentication
3. **Integration Planning**: Plan and implement enterprise tool integrations
4. **Monitoring Setup**: Implement comprehensive monitoring and alerting
5. **Performance Optimization**: Monitor and optimize system performance

For detailed information on specific components:

* :doc:`multi_tenancy` - Multi-tenancy and organization management
* :doc:`enterprise_integrations` - SSO and enterprise tool integrations
* :doc:`compliance_governance` - Compliance frameworks and governance
* :doc:`scalability_performance` - Performance optimization and scaling
