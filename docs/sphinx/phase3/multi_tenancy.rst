Multi-Tenancy & Organization Management
======================================

Phase 3 introduces comprehensive multi-tenancy capabilities, enabling RegressionRigor to serve multiple organizations with complete data isolation, resource management, and subscription-based access control.

.. contents:: Table of Contents
   :local:
   :depth: 2

Overview
--------

The multi-tenancy system provides:

* **Complete Data Isolation**: Each organization operates in a fully isolated environment
* **Flexible Subscription Tiers**: Support for Starter, Professional, Enterprise, and Custom plans
* **Resource Management**: Granular control over resource allocation and usage
* **User Management**: Organization-level user management with role-based access control
* **Billing Integration**: Usage tracking and billing support for SaaS deployments

Architecture
------------

Multi-Tenant Data Model
~~~~~~~~~~~~~~~~~~~~~~

The multi-tenancy architecture is built around the Organization entity, which serves as the primary tenant boundary:

.. mermaid::

   graph TD
       A[Organization] --> B[Users]
       A --> C[Campaigns]
       A --> D[Assessments]
       A --> E[Test Cases]
       A --> F[SSO Providers]
       A --> G[Integrations]
       A --> H[Compliance Frameworks]
       
       I[Tenant Resources] --> A
       J[Usage Metrics] --> A
       K[Billing Data] --> A
       
       style A fill:#e1f5fe
       style I fill:#f3e5f5
       style J fill:#f3e5f5
       style K fill:#f3e5f5

Data Isolation Strategy
~~~~~~~~~~~~~~~~~~~~~~

RegressionRigor implements **Row-Level Security (RLS)** combined with **Application-Level Filtering**:

1. **Database Level**: All tenant-specific tables include an ``organization_id`` column
2. **Application Level**: All queries automatically filter by organization context
3. **API Level**: Organization validation on every request
4. **Service Level**: Tenant context propagation through service layers

Organization Management
----------------------

Organization Model
~~~~~~~~~~~~~~~~~

.. code-block:: python

   class Organization(Base, TimestampMixin, SoftDeleteMixin):
       """Enhanced organization model with enterprise features."""
       
       id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4)
       name = Column(String(255), nullable=False, unique=True)
       display_name = Column(String(255), nullable=False)
       description = Column(Text, nullable=True)
       
       # Subscription and billing
       subscription_tier = Column(Enum(SubscriptionTier), nullable=False)
       subscription_status = Column(Enum(SubscriptionStatus), default=SubscriptionStatus.ACTIVE)
       billing_email = Column(String(255), nullable=True)
       
       # Settings and configuration
       settings = Column(JSON, nullable=False, default=dict)
       compliance_requirements = Column(JSON, nullable=False, default=list)
       
       # Contact information
       contact_info = Column(JSON, nullable=False, default=dict)
       
       # Usage and limits
       current_usage = Column(JSON, nullable=False, default=dict)
       usage_limits = Column(JSON, nullable=False, default=dict)

Subscription Tiers
~~~~~~~~~~~~~~~~~

.. list-table:: Subscription Tier Features
   :header-rows: 1
   :widths: 20 20 20 20 20

   * - Feature
     - Starter
     - Professional
     - Enterprise
     - Custom
   * - Max Users
     - 10
     - 100
     - 1,000
     - Unlimited
   * - Max Campaigns
     - 5
     - 50
     - 500
     - Unlimited
   * - Max Assessments
     - 20
     - 200
     - 2,000
     - Unlimited
   * - SSO Providers
     - 1
     - 3
     - Unlimited
     - Unlimited
   * - Integrations
     - 2
     - 10
     - Unlimited
     - Unlimited
   * - Data Retention
     - 90 days
     - 1 year
     - 7 years
     - Custom
   * - Support Level
     - Community
     - Business
     - Enterprise
     - Premium

Organization API
~~~~~~~~~~~~~~~

Creating an Organization
^^^^^^^^^^^^^^^^^^^^^^^

.. http:post:: /api/v4/organizations

   Create a new organization with specified subscription tier and settings.

   **Request Body:**

   .. code-block:: json

      {
        "name": "acme-corp",
        "display_name": "ACME Corporation",
        "description": "Leading cybersecurity consulting firm",
        "subscription_tier": "enterprise",
        "contact_info": {
          "primary_contact": "<EMAIL>",
          "phone": "******-0123",
          "address": {
            "street": "123 Security Blvd",
            "city": "Cyber City",
            "state": "CA",
            "zip": "90210",
            "country": "US"
          }
        },
        "settings": {
          "max_users": 1000,
          "max_campaigns": 500,
          "max_assessments": 2000,
          "data_retention_days": 2555,
          "security_settings": {
            "enforce_mfa": true,
            "session_timeout_minutes": 480,
            "password_policy": {
              "min_length": 12,
              "require_uppercase": true,
              "require_lowercase": true,
              "require_numbers": true,
              "require_symbols": true
            }
          }
        },
        "compliance_requirements": [
          "soc2_type2",
          "iso27001",
          "nist_csf"
        ]
      }

   **Response:**

   .. code-block:: json

      {
        "id": "123e4567-e89b-12d3-a456-************",
        "name": "acme-corp",
        "display_name": "ACME Corporation",
        "subscription_tier": "enterprise",
        "subscription_status": "active",
        "created_at": "2025-06-20T10:00:00Z",
        "current_usage": {
          "users": 0,
          "campaigns": 0,
          "assessments": 0,
          "storage_gb": 0
        },
        "usage_limits": {
          "max_users": 1000,
          "max_campaigns": 500,
          "max_assessments": 2000,
          "max_storage_gb": 1000
        }
      }

Managing Organization Users
^^^^^^^^^^^^^^^^^^^^^^^^^^

.. http:post:: /api/v4/organizations/{organization_id}/users

   Add a user to an organization with specified role and permissions.

   **Request Body:**

   .. code-block:: json

      {
        "email": "<EMAIL>",
        "role": "security_analyst",
        "permissions": [
          "campaigns.read",
          "campaigns.create",
          "assessments.read",
          "assessments.create",
          "reports.read"
        ],
        "send_invitation": true,
        "invitation_message": "Welcome to ACME Corporation's security testing platform!"
      }

.. http:get:: /api/v4/organizations/{organization_id}/usage

   Get current usage metrics for an organization.

   **Response:**

   .. code-block:: json

      {
        "organization_id": "123e4567-e89b-12d3-a456-************",
        "current_usage": {
          "users": 45,
          "campaigns": 23,
          "assessments": 156,
          "storage_gb": 12.5,
          "api_requests_last_hour": 1250,
          "sso_authentications_last_24h": 89
        },
        "usage_limits": {
          "max_users": 1000,
          "max_campaigns": 500,
          "max_assessments": 2000,
          "max_storage_gb": 1000,
          "api_requests_per_hour": 10000
        },
        "usage_percentages": {
          "users": 4.5,
          "campaigns": 4.6,
          "assessments": 7.8,
          "storage": 1.25,
          "api_requests": 12.5
        },
        "billing_period": {
          "start": "2025-06-01T00:00:00Z",
          "end": "2025-06-30T23:59:59Z"
        }
      }

Tenant Resource Management
-------------------------

Resource Allocation
~~~~~~~~~~~~~~~~~~

Each organization receives dedicated resources based on their subscription tier:

.. code-block:: python

   class TenantResource(Base, TimestampMixin):
       """Tenant resource allocation and tracking."""
       
       id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4)
       organization_id = Column(UUID(as_uuid=True), ForeignKey("organizations.id"))
       
       resource_type = Column(Enum(ResourceType), nullable=False)
       allocated_amount = Column(Integer, nullable=False)
       used_amount = Column(Integer, default=0, nullable=False)
       
       # Resource limits and quotas
       soft_limit = Column(Integer, nullable=True)  # Warning threshold
       hard_limit = Column(Integer, nullable=False)  # Absolute limit
       
       # Usage tracking
       last_usage_check = Column(DateTime, nullable=True)
       usage_history = Column(JSON, nullable=False, default=list)

Resource Types
~~~~~~~~~~~~~

.. list-table:: Resource Types and Allocation
   :header-rows: 1
   :widths: 25 25 50

   * - Resource Type
     - Unit
     - Description
   * - COMPUTE
     - CPU Hours
     - Processing time for assessments and analysis
   * - STORAGE
     - GB
     - Data storage for campaigns, results, and artifacts
   * - NETWORK
     - GB Transfer
     - Network bandwidth for integrations and API calls
   * - DATABASE
     - Connections
     - Concurrent database connections
   * - CACHE
     - MB
     - Redis cache allocation for performance
   * - API_CALLS
     - Requests/Hour
     - API rate limiting and usage tracking

Resource Monitoring
~~~~~~~~~~~~~~~~~~

.. http:get:: /api/v4/tenants/{organization_id}/resources

   Get detailed resource allocation and usage for a tenant.

   **Response:**

   .. code-block:: json

      {
        "organization_id": "123e4567-e89b-12d3-a456-************",
        "resources": [
          {
            "resource_type": "compute",
            "allocated_amount": 1000,
            "used_amount": 245,
            "soft_limit": 800,
            "hard_limit": 1000,
            "usage_percentage": 24.5,
            "status": "healthy"
          },
          {
            "resource_type": "storage",
            "allocated_amount": 1000,
            "used_amount": 125,
            "soft_limit": 800,
            "hard_limit": 1000,
            "usage_percentage": 12.5,
            "status": "healthy"
          }
        ],
        "overall_health": "healthy",
        "alerts": [],
        "recommendations": [
          {
            "type": "optimization",
            "message": "Consider archiving old campaign data to reduce storage usage",
            "priority": "low"
          }
        ]
      }

User Invitation System
---------------------

Email-Based Invitations
~~~~~~~~~~~~~~~~~~~~~~

The platform includes a comprehensive user invitation system:

.. code-block:: python

   class OrganizationInvitation(Base, TimestampMixin):
       """User invitation to organization."""
       
       id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4)
       organization_id = Column(UUID(as_uuid=True), ForeignKey("organizations.id"))
       
       email = Column(String(255), nullable=False)
       role = Column(String(100), nullable=False)
       permissions = Column(JSON, nullable=False, default=list)
       
       # Invitation tracking
       invited_by = Column(UUID(as_uuid=True), ForeignKey("users.id"))
       invitation_token = Column(String(255), nullable=False, unique=True)
       expires_at = Column(DateTime, nullable=False)
       
       # Status tracking
       status = Column(Enum(InvitationStatus), default=InvitationStatus.PENDING)
       accepted_at = Column(DateTime, nullable=True)
       accepted_by = Column(UUID(as_uuid=True), nullable=True)

Invitation Workflow
~~~~~~~~~~~~~~~~~~

.. mermaid::

   sequenceDiagram
       participant Admin as Organization Admin
       participant API as RegressionRigor API
       participant Email as Email Service
       participant User as Invited User
       
       Admin->>API: POST /api/v4/organizations/{id}/users
       API->>API: Generate invitation token
       API->>Email: Send invitation email
       Email->>User: Invitation email with link
       User->>API: GET /api/v4/invitations/{token}
       API->>User: Registration/login form
       User->>API: POST /api/v4/invitations/{token}/accept
       API->>API: Create user account
       API->>Admin: Notification of acceptance

Billing and Usage Tracking
--------------------------

Usage Metrics Collection
~~~~~~~~~~~~~~~~~~~~~~~

The platform automatically tracks usage across multiple dimensions:

.. code-block:: python

   class UsageMetric(Base, TimestampMixin):
       """Usage metric tracking for billing."""
       
       id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4)
       organization_id = Column(UUID(as_uuid=True), ForeignKey("organizations.id"))
       
       metric_type = Column(String(100), nullable=False)
       metric_value = Column(Float, nullable=False)
       metric_unit = Column(String(50), nullable=False)
       
       # Time-based tracking
       measurement_time = Column(DateTime, nullable=False, default=func.now())
       billing_period = Column(String(20), nullable=False)  # YYYY-MM format
       
       # Metadata
       metadata = Column(JSON, nullable=True)

Billing Integration
~~~~~~~~~~~~~~~~~~

.. http:get:: /api/v4/organizations/{organization_id}/billing

   Get billing information and usage summary for an organization.

   **Response:**

   .. code-block:: json

      {
        "organization_id": "123e4567-e89b-12d3-a456-************",
        "billing_period": "2025-06",
        "subscription_tier": "enterprise",
        "base_cost": 5000.00,
        "usage_charges": [
          {
            "metric": "additional_users",
            "quantity": 50,
            "unit_cost": 25.00,
            "total_cost": 1250.00
          },
          {
            "metric": "additional_storage_gb",
            "quantity": 100,
            "unit_cost": 2.00,
            "total_cost": 200.00
          }
        ],
        "total_cost": 6450.00,
        "currency": "USD",
        "billing_date": "2025-07-01T00:00:00Z"
      }

Best Practices
--------------

Organization Setup
~~~~~~~~~~~~~~~~~

1. **Naming Convention**: Use consistent, descriptive organization names
2. **Contact Information**: Maintain accurate contact information for billing and support
3. **Security Settings**: Configure appropriate security policies for your organization
4. **Resource Planning**: Monitor usage patterns and adjust limits proactively
5. **User Management**: Implement proper role-based access control

Data Isolation
~~~~~~~~~~~~~

1. **Query Filtering**: Always include organization context in database queries
2. **API Validation**: Validate organization access on every API request
3. **Cross-Tenant Prevention**: Implement checks to prevent cross-tenant data access
4. **Audit Logging**: Log all cross-organizational access attempts
5. **Testing**: Regularly test data isolation boundaries

Performance Optimization
~~~~~~~~~~~~~~~~~~~~~~~

1. **Database Indexing**: Ensure proper indexing on organization_id columns
2. **Query Optimization**: Optimize queries for multi-tenant performance
3. **Caching Strategy**: Implement organization-aware caching
4. **Resource Monitoring**: Monitor per-tenant resource usage
5. **Scaling Strategy**: Plan for horizontal scaling as tenant count grows

Next Steps
----------

* :doc:`enterprise_integrations` - Configure SSO and enterprise tool integrations
* :doc:`compliance_governance` - Implement compliance frameworks
* :doc:`implementation_guide` - Complete setup and deployment guide
