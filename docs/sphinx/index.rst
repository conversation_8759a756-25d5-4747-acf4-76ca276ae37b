Welcome to RegressionRigor Platform Documentation
===============================================

.. image:: _static/logo.png
   :alt: RegressionRigor Platform Logo
   :align: center
   :width: 200px

**RegressionRigor Platform** is a comprehensive cybersecurity testing and adversarial emulation platform for managing regression testing, security assessments, and quality assurance processes.

.. toctree::
   :maxdepth: 2
   :caption: Getting Started:

   getting_started/index

.. toctree::
   :maxdepth: 2
   :caption: User Documentation:

   user_guide/index
   api/index

.. toctree::
   :maxdepth: 2
   :caption: Cybersecurity Frameworks:

   frameworks/index

.. toctree::
   :maxdepth: 2
   :caption: Security & Implementation:

   security/index
   testing/index

.. toctree::
   :maxdepth: 2
   :caption: Development & Tools:

   development/index
   tools/index

.. toctree::
   :maxdepth: 2
   :caption: Administration:

   admin_guide/index
   deployment/index

.. toctree::
   :maxdepth: 2
   :caption: Phase 3 Enterprise Features:

   phase3/index

.. toctree::
   :maxdepth: 2
   :caption: Reference:

   reference/index
   phase1b/index
   contributing
   code_of_conduct
   changelog

Platform Overview
-----------------

RegressionRigor is an advanced cybersecurity platform that provides:

🏛️ **Cybersecurity Framework Management**
   * Multi-framework support (ISF, NIST CSF 2.0, ISO 27001, CIS Controls)
   * Cross-framework mapping and analytics
   * Compliance tracking and gap analysis
   * Implementation roadmaps and maturity assessment

🔒 **Security Testing & Assessment**
   * MITRE ATT&CK framework integration
   * Adversarial emulation capabilities
   * Security control validation
   * Breach & Attack Simulation (BAS)

⚡ **Development & Quality Assurance**
   * Comprehensive regression testing management
   * Automated code quality tools
   * Performance optimization utilities
   * Testing framework automation

🛡️ **Enterprise Security Features**
   * Multi-tenancy and organization management
   * Single Sign-On (SSO) integration (SAML2, OIDC, OAuth2, LDAP)
   * Enterprise security tool connectors (SIEM, SOAR, TIP)
   * Role-based access control and governance policies
   * Comprehensive audit logging and compliance tracking

📊 **Analytics & Reporting**
   * Executive dashboards with KPI tracking
   * Business intelligence datasets and custom analytics
   * Real-time heat map visualizations and trend analysis
   * Cross-organizational benchmarking and predictive analytics
   * Automated reporting and compliance gap analysis

🔧 **Developer Tools**
   * 22+ development automation scripts
   * Code quality and PEP compliance tools
   * Performance analysis and optimization
   * Comprehensive testing utilities

Quick Navigation
---------------

.. grid:: 2
   :gutter: 3

   .. grid-item-card:: 🚀 Getting Started
      :link: getting_started/index
      :link-type: doc

      New to RegressionRigor? Start here for installation, setup, and your first steps.

   .. grid-item-card:: 🏛️ Cybersecurity Frameworks
      :link: frameworks/index
      :link-type: doc

      Comprehensive framework management with ISF, NIST CSF 2.0, ISO 27001, and CIS Controls.

   .. grid-item-card:: 🔒 Security Implementation
      :link: security/index
      :link-type: doc

      Security guidelines, authentication, input validation, and best practices.

   .. grid-item-card:: 🔧 Development Tools
      :link: tools/index
      :link-type: doc

      Comprehensive suite of development tools for code quality and performance.

   .. grid-item-card:: 📚 API Reference
      :link: api/index
      :link-type: doc

      Complete API documentation with examples and interactive guides.

   .. grid-item-card:: 🏢 Phase 3 Enterprise Features
      :link: phase3/index
      :link-type: doc

      Multi-tenancy, SSO integration, enterprise tool connectors, and business intelligence.

Indices and tables
==================

* :ref:`genindex`
* :ref:`modindex`
* :ref:`search`
